{"name": "FFDV Weight Constraint Test", "description": "Test case for the FirstFitDecreasingVolumeAlgorithm with weight constraints", "bin": {"width": 100, "height": 100, "depth": 100, "maxWeight": 500, "overhangX": 0, "overhangY": 0}, "boxTypes": [{"id": "heavy_box", "width": 50, "height": 50, "depth": 50, "weight": 300, "quantity": 1, "shapeType": "Cuboid"}, {"id": "medium_box", "width": 40, "height": 40, "depth": 40, "weight": 150, "quantity": 2, "shapeType": "Cuboid"}, {"id": "light_box", "width": 30, "height": 30, "depth": 30, "weight": 50, "quantity": 4, "shapeType": "Cuboid"}], "algorithmType": "FirstFitDecreasingVolume", "algorithmSettings": {"verticalConstraint": "ANY"}}