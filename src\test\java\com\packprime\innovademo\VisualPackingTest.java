package com.packprime.innovademo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.packprime.innovademo.algorithm.Bin;
import com.packprime.innovademo.algorithm.FirstFitDecreasingVolumeAlgorithm;
import com.packprime.innovademo.algorithm.PackingAlgorithm;
import com.packprime.innovademo.algorithm.PackingResult;
import com.packprime.innovademo.algorithm.Piece;
import com.packprime.innovademo.algorithm.VerticalOrientationConstraint;
import com.packprime.innovademo.dto.PackingRequest;
import com.packprime.innovademo.model.BinDefinition;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Visual Packing Test class for generating test data for the frontend visualization.
 * This class creates test cases for the FirstFitDecreasingVolumeAlgorithm and saves them
 * as JSON files that can be used by the frontend to visualize the packing results.
 */
public class VisualPackingTest {

    private static final Logger log = LoggerFactory.getLogger(VisualPackingTest.class);
    private static final String OUTPUT_DIR = "src/main/resources/static/industry_data";
    private ObjectMapper objectMapper;
    private PackingAlgorithm algorithm;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        algorithm = new FirstFitDecreasingVolumeAlgorithm();
        
        // Ensure the output directory exists
        File outputDir = new File(OUTPUT_DIR);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
    }

    /**
     * Helper method to create a default BinDefinition for tests
     */
    private BinDefinition createDefaultBinDef(double maxWeight) {
        BinDefinition binDef = new BinDefinition();
        binDef.setMaxWeight(maxWeight);
        binDef.setOverhangX(0);
        binDef.setOverhangY(0);
        return binDef;
    }

    /**
     * Helper method to create a Piece with default constraints for tests
     */
    private Piece createPiece(String id, double w, double h, double d, double weight) {
        // Using default values for value, rotation, stacking, priority, quantity
        return new Piece(id, w, h, d, 0.0, weight, true, true, true, 99, 0, 1, 1);
    }

    /**
     * Helper method to create a test case and save it as a JSON file
     */
    private void createTestCase(String filename, String name, String description, 
                               BinDefinition binDef, List<Map<String, Object>> boxTypes,
                               String algorithmType, Map<String, Object> algorithmSettings) throws IOException {
        Map<String, Object> testCase = new HashMap<>();
        testCase.put("name", name);
        testCase.put("description", description);
        
        Map<String, Object> bin = new HashMap<>();
        bin.put("width", binDef.getWidth());
        bin.put("height", binDef.getHeight());
        bin.put("depth", binDef.getDepth());
        bin.put("maxWeight", binDef.getMaxWeight());
        bin.put("overhangX", binDef.getOverhangX());
        bin.put("overhangY", binDef.getOverhangY());
        testCase.put("bin", bin);
        
        testCase.put("boxTypes", boxTypes);
        testCase.put("algorithmType", algorithmType);
        testCase.put("algorithmSettings", algorithmSettings);
        
        // Write to file
        objectMapper.writerWithDefaultPrettyPrinter()
            .writeValue(new File(OUTPUT_DIR + "/" + filename), testCase);
        
        log.info("Created test case: {}", filename);
    }

    /**
     * Test that generates a simple test case with a mix of box sizes
     */
    @Test
    void generateSimpleTestCase() throws IOException {
        // Define the bin
        BinDefinition binDef = createDefaultBinDef(1000);
        binDef.setWidth(100);
        binDef.setHeight(100);
        binDef.setDepth(100);
        
        // Define box types
        List<Map<String, Object>> boxTypes = new ArrayList<>();
        
        Map<String, Object> largeBox = new HashMap<>();
        largeBox.put("id", "large_box");
        largeBox.put("width", 60);
        largeBox.put("height", 60);
        largeBox.put("depth", 60);
        largeBox.put("weight", 200);
        largeBox.put("quantity", 1);
        largeBox.put("shapeType", "Cuboid");
        boxTypes.add(largeBox);
        
        Map<String, Object> mediumBox = new HashMap<>();
        mediumBox.put("id", "medium_box");
        mediumBox.put("width", 40);
        mediumBox.put("height", 40);
        mediumBox.put("depth", 40);
        mediumBox.put("weight", 100);
        mediumBox.put("quantity", 2);
        mediumBox.put("shapeType", "Cuboid");
        boxTypes.add(mediumBox);
        
        Map<String, Object> smallBox = new HashMap<>();
        smallBox.put("id", "small_box");
        smallBox.put("width", 20);
        smallBox.put("height", 20);
        smallBox.put("depth", 20);
        smallBox.put("weight", 50);
        smallBox.put("quantity", 4);
        smallBox.put("shapeType", "Cuboid");
        boxTypes.add(smallBox);
        
        // Define algorithm settings
        Map<String, Object> algorithmSettings = new HashMap<>();
        algorithmSettings.put("verticalConstraint", "ANY");
        
        // Create and save the test case
        createTestCase(
            "ffdv_simple_test_generated.json",
            "Simple FFDV Test (Generated)",
            "A simple test case for the FirstFitDecreasingVolumeAlgorithm with a mix of box sizes",
            binDef,
            boxTypes,
            "FirstFitDecreasingVolume",
            algorithmSettings
        );
    }

    /**
     * Test that generates a test case with vertical orientation constraints
     */
    @Test
    void generateVerticalConstraintTestCase() throws IOException {
        // Define the bin
        BinDefinition binDef = createDefaultBinDef(1000);
        binDef.setWidth(100);
        binDef.setHeight(100);
        binDef.setDepth(100);
        
        // Define box types
        List<Map<String, Object>> boxTypes = new ArrayList<>();
        
        Map<String, Object> tallBox = new HashMap<>();
        tallBox.put("id", "tall_box");
        tallBox.put("width", 30);
        tallBox.put("height", 80);
        tallBox.put("depth", 30);
        tallBox.put("weight", 150);
        tallBox.put("quantity", 2);
        
        Map<String, Object> allowedOrientations = new HashMap<>();
        allowedOrientations.put("width", false);
        allowedOrientations.put("height", true);
        allowedOrientations.put("depth", false);
        tallBox.put("allowedVerticalOrientations", allowedOrientations);
        tallBox.put("shapeType", "Cuboid");
        boxTypes.add(tallBox);
        
        Map<String, Object> wideBox = new HashMap<>();
        wideBox.put("id", "wide_box");
        wideBox.put("width", 70);
        wideBox.put("height", 40);
        wideBox.put("depth", 30);
        wideBox.put("weight", 180);
        wideBox.put("quantity", 1);
        
        Map<String, Object> wideBoxOrientations = new HashMap<>();
        wideBoxOrientations.put("width", true);
        wideBoxOrientations.put("height", false);
        wideBoxOrientations.put("depth", false);
        wideBox.put("allowedVerticalOrientations", wideBoxOrientations);
        wideBox.put("shapeType", "Cuboid");
        boxTypes.add(wideBox);
        
        // Define algorithm settings
        Map<String, Object> algorithmSettings = new HashMap<>();
        algorithmSettings.put("verticalConstraint", "HEIGHT_ONLY");
        
        // Create and save the test case
        createTestCase(
            "ffdv_vertical_constraint_test_generated.json",
            "FFDV Vertical Constraint Test (Generated)",
            "Test case for the FirstFitDecreasingVolumeAlgorithm with vertical orientation constraints",
            binDef,
            boxTypes,
            "FirstFitDecreasingVolume",
            algorithmSettings
        );
    }

    /**
     * Test that generates a test case with mixed shapes (cuboids and cylinders)
     */
    @Test
    void generateMixedShapesTestCase() throws IOException {
        // Define the bin
        BinDefinition binDef = createDefaultBinDef(1000);
        binDef.setWidth(120);
        binDef.setHeight(100);
        binDef.setDepth(120);
        
        // Define box types
        List<Map<String, Object>> boxTypes = new ArrayList<>();
        
        Map<String, Object> largeCylinder = new HashMap<>();
        largeCylinder.put("id", "large_cylinder");
        largeCylinder.put("width", 50);
        largeCylinder.put("height", 50);
        largeCylinder.put("depth", 70);
        largeCylinder.put("weight", 180);
        largeCylinder.put("quantity", 1);
        largeCylinder.put("shapeType", "Cylinder");
        largeCylinder.put("diameter", 50);
        boxTypes.add(largeCylinder);
        
        Map<String, Object> mediumCylinder = new HashMap<>();
        mediumCylinder.put("id", "medium_cylinder");
        mediumCylinder.put("width", 30);
        mediumCylinder.put("height", 30);
        mediumCylinder.put("depth", 50);
        mediumCylinder.put("weight", 100);
        mediumCylinder.put("quantity", 2);
        mediumCylinder.put("shapeType", "Cylinder");
        mediumCylinder.put("diameter", 30);
        boxTypes.add(mediumCylinder);
        
        Map<String, Object> largeBox = new HashMap<>();
        largeBox.put("id", "large_box");
        largeBox.put("width", 60);
        largeBox.put("height", 40);
        largeBox.put("depth", 50);
        largeBox.put("weight", 150);
        largeBox.put("quantity", 1);
        largeBox.put("shapeType", "Cuboid");
        boxTypes.add(largeBox);
        
        // Define algorithm settings
        Map<String, Object> algorithmSettings = new HashMap<>();
        algorithmSettings.put("verticalConstraint", "ANY");
        
        // Create and save the test case
        createTestCase(
            "ffdv_mixed_shapes_test_generated.json",
            "FFDV Mixed Shapes Test (Generated)",
            "Test case for the FirstFitDecreasingVolumeAlgorithm with mixed shapes (cuboids and cylinders)",
            binDef,
            boxTypes,
            "FirstFitDecreasingVolume",
            algorithmSettings
        );
    }

    /**
     * Test that generates a test case with weight constraints
     */
    @Test
    void generateWeightConstraintTestCase() throws IOException {
        // Define the bin
        BinDefinition binDef = createDefaultBinDef(500);
        binDef.setWidth(100);
        binDef.setHeight(100);
        binDef.setDepth(100);
        
        // Define box types
        List<Map<String, Object>> boxTypes = new ArrayList<>();
        
        Map<String, Object> heavyBox = new HashMap<>();
        heavyBox.put("id", "heavy_box");
        heavyBox.put("width", 50);
        heavyBox.put("height", 50);
        heavyBox.put("depth", 50);
        heavyBox.put("weight", 300);
        heavyBox.put("quantity", 1);
        heavyBox.put("shapeType", "Cuboid");
        boxTypes.add(heavyBox);
        
        Map<String, Object> mediumBox = new HashMap<>();
        mediumBox.put("id", "medium_box");
        mediumBox.put("width", 40);
        mediumBox.put("height", 40);
        mediumBox.put("depth", 40);
        mediumBox.put("weight", 150);
        mediumBox.put("quantity", 2);
        mediumBox.put("shapeType", "Cuboid");
        boxTypes.add(mediumBox);
        
        Map<String, Object> lightBox = new HashMap<>();
        lightBox.put("id", "light_box");
        lightBox.put("width", 30);
        lightBox.put("height", 30);
        lightBox.put("depth", 30);
        lightBox.put("weight", 50);
        lightBox.put("quantity", 4);
        lightBox.put("shapeType", "Cuboid");
        boxTypes.add(lightBox);
        
        // Define algorithm settings
        Map<String, Object> algorithmSettings = new HashMap<>();
        algorithmSettings.put("verticalConstraint", "ANY");
        
        // Create and save the test case
        createTestCase(
            "ffdv_weight_constraint_test_generated.json",
            "FFDV Weight Constraint Test (Generated)",
            "Test case for the FirstFitDecreasingVolumeAlgorithm with weight constraints",
            binDef,
            boxTypes,
            "FirstFitDecreasingVolume",
            algorithmSettings
        );
    }
    
    /**
     * Test that generates a test case with a complex arrangement of boxes
     */
    @Test
    void generateComplexArrangementTestCase() throws IOException {
        // Define the bin
        BinDefinition binDef = createDefaultBinDef(2000);
        binDef.setWidth(200);
        binDef.setHeight(150);
        binDef.setDepth(200);
        
        // Define box types
        List<Map<String, Object>> boxTypes = new ArrayList<>();
        
        // Add a variety of box sizes and shapes
        Map<String, Object> extraLargeBox = new HashMap<>();
        extraLargeBox.put("id", "extra_large_box");
        extraLargeBox.put("width", 100);
        extraLargeBox.put("height", 80);
        extraLargeBox.put("depth", 100);
        extraLargeBox.put("weight", 500);
        extraLargeBox.put("quantity", 1);
        extraLargeBox.put("shapeType", "Cuboid");
        boxTypes.add(extraLargeBox);
        
        Map<String, Object> flatBox = new HashMap<>();
        flatBox.put("id", "flat_box");
        flatBox.put("width", 80);
        flatBox.put("height", 20);
        flatBox.put("depth", 80);
        flatBox.put("weight", 150);
        flatBox.put("quantity", 2);
        flatBox.put("shapeType", "Cuboid");
        boxTypes.add(flatBox);
        
        Map<String, Object> tallThinBox = new HashMap<>();
        tallThinBox.put("id", "tall_thin_box");
        tallThinBox.put("width", 30);
        tallThinBox.put("height", 100);
        tallThinBox.put("depth", 30);
        tallThinBox.put("weight", 120);
        tallThinBox.put("quantity", 3);
        tallThinBox.put("shapeType", "Cuboid");
        boxTypes.add(tallThinBox);
        
        Map<String, Object> mediumBox = new HashMap<>();
        mediumBox.put("id", "medium_box");
        mediumBox.put("width", 50);
        mediumBox.put("height", 50);
        mediumBox.put("depth", 50);
        mediumBox.put("weight", 200);
        mediumBox.put("quantity", 2);
        mediumBox.put("shapeType", "Cuboid");
        boxTypes.add(mediumBox);
        
        Map<String, Object> smallBox = new HashMap<>();
        smallBox.put("id", "small_box");
        smallBox.put("width", 25);
        smallBox.put("height", 25);
        smallBox.put("depth", 25);
        smallBox.put("weight", 50);
        smallBox.put("quantity", 8);
        smallBox.put("shapeType", "Cuboid");
        boxTypes.add(smallBox);
        
        Map<String, Object> largeCylinder = new HashMap<>();
        largeCylinder.put("id", "large_cylinder");
        largeCylinder.put("width", 60);
        largeCylinder.put("height", 60);
        largeCylinder.put("depth", 80);
        largeCylinder.put("weight", 300);
        largeCylinder.put("quantity", 1);
        largeCylinder.put("shapeType", "Cylinder");
        largeCylinder.put("diameter", 60);
        boxTypes.add(largeCylinder);
        
        // Define algorithm settings
        Map<String, Object> algorithmSettings = new HashMap<>();
        algorithmSettings.put("verticalConstraint", "ANY");
        
        // Create and save the test case
        createTestCase(
            "ffdv_complex_arrangement_test.json",
            "FFDV Complex Arrangement Test",
            "Test case for the FirstFitDecreasingVolumeAlgorithm with a complex arrangement of boxes",
            binDef,
            boxTypes,
            "FirstFitDecreasingVolume",
            algorithmSettings
        );
    }
}
