/**
 * Results Table Component for Packing Algorithm Frontend
 * This component displays the results of the packing algorithm
 */

import React from 'react';
import { PackingResponse } from '@/types';
import { cn, formatDimensions, formatPosition, formatOrientation, getBaseId } from '@/lib/utils';

interface ResultsTableProps {
  packingResult?: PackingResponse;
  primaryPackNames: Record<string, string>; // Maps piece IDs to their names
  className?: string;
}

const ResultsTable: React.FC<ResultsTableProps> = ({
  packingResult,
  primaryPackNames,
  className,
}) => {
  // If no results, show a message
  if (!packingResult) {
    return (
      <div className={cn("p-4 bg-white dark:bg-gray-800 rounded-lg shadow", className)}>
        <h2 className="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">Packing Results</h2>
        <p className="text-gray-500 dark:text-gray-400">No packing results available. Click "Pack Items" to start.</p>
      </div>
    );
  }

  // Get all pieces (placed and unplaced)
  const placedPieces = packingResult.placedPieces || [];
  const unplacedPieceIds = packingResult.unplacedPieceIds || [];

  // Create unplaced pieces array for display
  const unplacedPieces: Array<{ id: string; baseId: string }> = unplacedPieceIds.map(id => ({
    id,
    baseId: getBaseId(id),
  }));

  return (
    <div className={cn("p-4 bg-white dark:bg-gray-800 rounded-lg shadow", className)}>
      <h2 className="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">Packing Results</h2>
      
      {/* Summary statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Volume Utilization</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {packingResult.volumeUtilization.toFixed(2)}%
          </p>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Weight</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {packingResult.totalWeight.toFixed(2)} kg
          </p>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Items Packed</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {placedPieces.length}/{placedPieces.length + unplacedPieceIds.length}
          </p>
        </div>
      </div>
      
      {/* Algorithm and status information */}
      <div className="mb-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Algorithm Used</h3>
          <p className="text-lg font-medium text-gray-900 dark:text-white">
            {packingResult.algorithmUsed || 'Default'}
          </p>
        </div>
        
        {packingResult.statusMessages && packingResult.statusMessages.length > 0 && (
          <div className="mt-3 bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-400">Status Messages</h3>
            <ul className="mt-2 list-disc list-inside text-yellow-700 dark:text-yellow-300">
              {packingResult.statusMessages.map((message, index) => (
                <li key={index}>{message}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {/* Placed items table */}
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Placed Items</h3>
      <div className="overflow-x-auto mb-6">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Original Dimensions (mm)</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Placed Dimensions (mm)</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Weight (kg)</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Position (X,Y,Z)</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Orientation</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
            {placedPieces.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  No items were placed.
                </td>
              </tr>
            ) : (
              placedPieces.map((piece, index) => {
                const baseId = getBaseId(piece.id);
                const name = primaryPackNames[baseId] || baseId;
                
                return (
                  <tr key={`${piece.id}_${index}`}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {formatDimensions(piece.originalWidth, piece.originalHeight, piece.originalDepth)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {formatDimensions(piece.placedWidth, piece.placedHeight, piece.placedDepth)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {piece.weight.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {formatPosition(piece.position.x, piece.position.y, piece.position.z)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {formatOrientation(piece.orientation)}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
      
      {/* Unplaced items table */}
      {unplacedPieces.length > 0 && (
        <>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Unplaced Items</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                {unplacedPieces.map((piece, index) => (
                  <tr key={`unplaced_${piece.id}_${index}`}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {primaryPackNames[piece.baseId] || piece.baseId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {piece.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                        Not Placed
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
};

export default ResultsTable;
