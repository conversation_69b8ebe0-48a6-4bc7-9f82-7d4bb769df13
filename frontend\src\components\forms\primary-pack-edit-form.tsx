/**
 * Primary Pack Edit Form Component
 * This component handles the form for adding/editing primary packs
 */

import React from 'react';
import { PieceDto } from '@/types';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Props for the PrimaryPackEditForm component
interface PrimaryPackEditFormProps {
  editingPack: PieceDto & { name: string; quantity: number };
  isAdding: boolean;
  onSave: () => void;
  onCancel: () => void;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  onSelectChange?: (name: string, value: string) => void;
}

/**
 * Form for editing primary pack details
 */
const PrimaryPackEditForm: React.FC<PrimaryPackEditFormProps> = ({
  editingPack,
  isAdding,
  onSave,
  onCancel,
  onChange,
  onSelectChange = () => {},
}) => {
  // Create a unique prefix for form field IDs to avoid duplicates
  const formIdPrefix = `edit-primary-pack-${editingPack.id}`;
  
  // Handle shape type change
  const handleShapeTypeChange = (value: string) => {
    onSelectChange('shapeType', value);
  };

  const isCylinder = editingPack.shapeType === 'Cylinder';
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-full max-w-xl max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {isAdding ? 'Add Primary Pack' : 'Edit Primary Pack'}
        </h3>
        
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <label htmlFor={`${formIdPrefix}-name`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name
              </label>
              <input
                type="text"
                id={`${formIdPrefix}-name`}
                name="name"
                value={editingPack.name}
                onChange={onChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter name"
              />
            </div>
            
            <div>
              <label htmlFor={`${formIdPrefix}-shapeType`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Shape Type
              </label>
              <Select 
                value={editingPack.shapeType || 'Cuboid'} 
                onValueChange={handleShapeTypeChange}
              >
                <SelectTrigger id={`${formIdPrefix}-shapeType`} className="w-[140px]">
                  <SelectValue placeholder="Select shape" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Cuboid">Cuboid</SelectItem>
                  <SelectItem value="Cylinder">Cylinder</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {editingPack.shapeType === 'Cylinder' ? (
              <>
                <div>
                  <label htmlFor={`${formIdPrefix}-diameter`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Diameter (mm)
                  </label>
                  <input
                    type="number"
                    id={`${formIdPrefix}-diameter`}
                    name="diameter"
                    value={editingPack.diameter || ''}
                    onChange={onChange}
                    min="1"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Diameter"
                  />
                </div>
                <div>
                  <label htmlFor={`${formIdPrefix}-depth`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Height (mm)
                  </label>
                  <input
                    type="number"
                    id={`${formIdPrefix}-depth`}
                    name="depth"
                    value={editingPack.depth || ''}
                    onChange={onChange}
                    min="1"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Height"
                  />
                </div>
              </>
            ) : (
              <>
                <div>
                  <label htmlFor={`${formIdPrefix}-width`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Length (mm)
                  </label>
                  <input
                    type="number"
                    id={`${formIdPrefix}-width`}
                    name="width"
                    value={editingPack.width || ''}
                    onChange={onChange}
                    min="1"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Width"
                  />
                </div>
                <div>
                  <label htmlFor={`${formIdPrefix}-height`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Width (mm)
                  </label>
                  <input
                    type="number"
                    id={`${formIdPrefix}-height`}
                    name="height"
                    value={editingPack.height || ''}
                    onChange={onChange}
                    min="1"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Length"
                  />
                </div>
                <div>
                  <label htmlFor={`${formIdPrefix}-depth`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Height (mm)
                  </label>
                  <input
                    type="number"
                    id={`${formIdPrefix}-depth`}
                    name="depth"
                    value={editingPack.depth || ''}
                    onChange={onChange}
                    min="1"
                    step="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Height"
                  />
                </div>
              </>
            )}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor={`${formIdPrefix}-weight`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Weight (kg)
              </label>
              <input
                type="number"
                id={`${formIdPrefix}-weight`}
                name="weight"
                value={editingPack.weight || ''}
                onChange={onChange}
                min="0.1"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Weight"
              />
            </div>
            
            <div>
              <label htmlFor={`${formIdPrefix}-quantity`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Quantity
              </label>
              <input
                type="number"
                id={`${formIdPrefix}-quantity`}
                name="quantity"
                value={editingPack.quantity || ''}
                onChange={onChange}
                min="1"
                step="1"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Quantity"
              />
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Dimension allowed vertical:</p>
            <div className="flex items-center space-x-4">
              {editingPack.shapeType === 'Cylinder' ? (
                <>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`${formIdPrefix}-allowRotationX`}
                      name="allowRotationX"
                      checked={editingPack.allowRotationX}
                      onChange={onChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
                    />
                    <label htmlFor={`${formIdPrefix}-allowRotationX`} className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                      Diameter
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`${formIdPrefix}-allowRotationZ`}
                      name="allowRotationZ"
                      checked={editingPack.allowRotationZ}
                      onChange={onChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
                    />
                    <label htmlFor={`${formIdPrefix}-allowRotationZ`} className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                      Height
                    </label>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`${formIdPrefix}-allowRotationX`}
                      name="allowRotationX"
                      checked={editingPack.allowRotationX}
                      onChange={onChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
                    />
                    <label htmlFor={`${formIdPrefix}-allowRotationX`} className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                      Length
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`${formIdPrefix}-allowRotationY`}
                      name="allowRotationY"
                      checked={editingPack.allowRotationY}
                      onChange={onChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
                    />
                    <label htmlFor={`${formIdPrefix}-allowRotationY`} className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                      Width
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`${formIdPrefix}-allowRotationZ`}
                      name="allowRotationZ"
                      checked={editingPack.allowRotationZ}
                      onChange={onChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
                    />
                    <label htmlFor={`${formIdPrefix}-allowRotationZ`} className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                      Height
                    </label>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={onSave}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
          >
            {isAdding ? 'Add' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrimaryPackEditForm;
