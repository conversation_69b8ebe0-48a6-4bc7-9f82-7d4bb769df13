package com.packprime.innovademo.algorithm;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents the result of a packing operation performed by a PackingAlgorithm. Contains lists of
 * placed and unplaced pieces, along with status messages.
 */
public class PackingResult {

  private final List<Piece> placedPieces;
  private final List<Piece> unplacedPieces;
  private final List<String> statusMessages;

  public PackingResult(
      List<Piece> placedPieces, List<Piece> unplacedPieces, List<String> statusMessages) {
    this.placedPieces = placedPieces != null ? List.copyOf(placedPieces) : List.of();
    this.unplacedPieces = unplacedPieces != null ? List.copyOf(unplacedPieces) : List.of();
    this.statusMessages = statusMessages != null ? List.copyOf(statusMessages) : List.of();
  }

  // Convenience constructor for simpler cases
  public PackingResult(List<Piece> placedPieces, List<Piece> unplacedPieces) {
    this(placedPieces, unplacedPieces, new ArrayList<>());
  }

  public List<Piece> getPlacedPieces() {
    return placedPieces;
  }

  public List<Piece> getUnplacedPieces() {
    return unplacedPieces;
  }

  public List<String> getStatusMessages() {
    return statusMessages;
  }

  // Helper method to add a status message (useful within algorithms)
  // Note: Since the main list is immutable, this isn't directly usable on the final object,
  // but algorithms can build up a mutable list internally and pass it to the constructor.
  // Alternatively, algorithms could return a mutable version or use a builder pattern.
  // For simplicity now, algorithms will likely build their own list.
}
