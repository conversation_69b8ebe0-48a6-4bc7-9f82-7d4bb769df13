/**
 * Custom hook for managing industry data files
 */

import { useState, useEffect, useCallback } from 'react';
import { IndustryDataState, IndustryDataFile } from '@/types/frontend-types';
import { getIndustryDataFiles, getIndustryDataFileContent } from '@/lib/api';

interface UseIndustryDataOptions {
  autoLoad?: boolean;
}

interface UseIndustryDataResult {
  industryData: IndustryDataState;
  loadIndustryDataFiles: () => Promise<void>;
  selectIndustryDataFile: (filename: string) => Promise<any>;
  clearSelectedFile: () => void;
  error: string | undefined;
  isLoading: boolean;
}

export function useIndustryData({ autoLoad = true }: UseIndustryDataOptions = {}): UseIndustryDataResult {
  const [industryData, setIndustryData] = useState<IndustryDataState>({
    availableFiles: [],
    isLoading: false,
  });
  const [error, setError] = useState<string | undefined>(undefined);

  // Load industry data files from the API
  const loadIndustryDataFiles = useCallback(async () => {
    try {
      setIndustryData((prev: IndustryDataState) => ({ ...prev, isLoading: true, error: undefined }));
      
      const filenames = await getIndustryDataFiles();
      
      // Convert filenames to display names (remove .json extension and format)
      const files: IndustryDataFile[] = filenames.map(filename => ({
        filename,
        displayName: filename
          .replace('.json', '')
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
      }));
      
      setIndustryData((prev: IndustryDataState) => ({ 
        ...prev, 
        availableFiles: files,
        isLoading: false 
      }));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load industry data files';
      setError(errorMessage);
      setIndustryData((prev: IndustryDataState) => ({ 
        ...prev, 
        error: errorMessage,
        isLoading: false 
      }));
    }
  }, []);

  // Select and load a specific industry data file
  const selectIndustryDataFile = useCallback(async (filename: string) => {
    try {
      setIndustryData((prev: IndustryDataState) => ({ ...prev, isLoading: true, error: undefined }));
      
      const fileContent = await getIndustryDataFileContent(filename);
      
      setIndustryData((prev: IndustryDataState) => ({ 
        ...prev, 
        selectedFile: filename,
        fileContent,
        isLoading: false 
      }));
      
      return fileContent;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to load industry data file ${filename}`;
      setError(errorMessage);
      setIndustryData((prev: IndustryDataState) => ({ 
        ...prev, 
        error: errorMessage,
        isLoading: false 
      }));
      throw err;
    }
  }, []);

  // Clear selected file
  const clearSelectedFile = useCallback(() => {
    setIndustryData((prev: IndustryDataState) => ({ 
      ...prev, 
      selectedFile: undefined,
      fileContent: undefined 
    }));
  }, []);

  // Auto-load industry data files on mount if autoLoad is true
  useEffect(() => {
    if (autoLoad) {
      loadIndustryDataFiles();
    }
  }, [autoLoad, loadIndustryDataFiles]);

  return {
    industryData,
    loadIndustryDataFiles,
    selectIndustryDataFile,
    clearSelectedFile,
    error,
    isLoading: industryData.isLoading,
  };
}
