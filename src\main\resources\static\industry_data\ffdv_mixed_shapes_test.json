{"name": "FFDV Mixed Shapes Test", "description": "Test case for the FirstFitDecreasingVolumeAlgorithm with mixed shapes (cuboids and cylinders)", "bin": {"width": 120, "height": 100, "depth": 120, "maxWeight": 1000, "overhangX": 0, "overhangY": 0}, "boxTypes": [{"id": "large_cylinder", "width": 50, "height": 50, "depth": 70, "weight": 180, "quantity": 1, "shapeType": "<PERSON><PERSON><PERSON>", "diameter": 50}, {"id": "medium_cylinder", "width": 30, "height": 30, "depth": 50, "weight": 100, "quantity": 2, "shapeType": "<PERSON><PERSON><PERSON>", "diameter": 30}, {"id": "large_box", "width": 60, "height": 40, "depth": 50, "weight": 150, "quantity": 1, "shapeType": "Cuboid"}, {"id": "small_box", "width": 30, "height": 20, "depth": 40, "weight": 70, "quantity": 3, "shapeType": "Cuboid"}], "algorithmType": "FirstFitDecreasingVolume", "algorithmSettings": {"verticalConstraint": "ANY"}}