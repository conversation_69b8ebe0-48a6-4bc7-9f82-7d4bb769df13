package com.packprime.innovademo.service;

import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for validating rule JSON structure.
 */
@Service
public class RuleValidationService {
    private static final Logger log = LoggerFactory.getLogger(RuleValidationService.class);
    
    /**
     * Validates a rule JSON structure.
     * 
     * @param ruleJson The rule JSON to validate
     * @return List of validation errors, empty if valid
     */
    public List<String> validateRule(JsonNode ruleJson) {
        List<String> errors = new ArrayList<>();
        
        // Check if the rule is a JSON object
        if (!ruleJson.isObject()) {
            errors.add("Rule must be a JSON object");
            return errors;
        }
        
        // Validate required fields
        validateRequiredFields(ruleJson, errors);
        
        // Validate bin
        if (ruleJson.has("bin")) {
            validateBin(ruleJson.get("bin"), errors);
        }
        
        // Validate box types
        if (ruleJson.has("boxTypes")) {
            validateBoxTypes(ruleJson.get("boxTypes"), errors);
        }
        
        // Validate algorithm settings
        if (ruleJson.has("algorithmSettings")) {
            validateAlgorithmSettings(ruleJson.get("algorithmSettings"), errors);
        }
        
        return errors;
    }
    
    /**
     * Validates required fields in the rule JSON.
     * 
     * @param ruleJson The rule JSON
     * @param errors List to add errors to
     */
    private void validateRequiredFields(JsonNode ruleJson, List<String> errors) {
        // Check for name
        if (!ruleJson.has("name") || ruleJson.get("name").asText().trim().isEmpty()) {
            errors.add("Rule must have a name");
        }
        
        // Check for bin
        if (!ruleJson.has("bin")) {
            errors.add("Rule must have a bin configuration");
        }
        
        // Check for box types
        if (!ruleJson.has("boxTypes")) {
            errors.add("Rule must have box types");
        } else if (!ruleJson.get("boxTypes").isArray() || ruleJson.get("boxTypes").size() == 0) {
            errors.add("Rule must have at least one box type");
        }
        
        // Check for algorithm type
        if (!ruleJson.has("algorithmType")) {
            errors.add("Rule must have an algorithm type");
        }
    }
    
    /**
     * Validates the bin configuration.
     * 
     * @param binJson The bin JSON
     * @param errors List to add errors to
     */
    private void validateBin(JsonNode binJson, List<String> errors) {
        if (!binJson.isObject()) {
            errors.add("Bin must be a JSON object");
            return;
        }
        
        // Check for required bin fields
        if (!binJson.has("width") || !binJson.get("width").isNumber() || binJson.get("width").asDouble() <= 0) {
            errors.add("Bin must have a positive width");
        }
        
        if (!binJson.has("height") || !binJson.get("height").isNumber() || binJson.get("height").asDouble() <= 0) {
            errors.add("Bin must have a positive height");
        }
        
        if (!binJson.has("depth") || !binJson.get("depth").isNumber() || binJson.get("depth").asDouble() <= 0) {
            errors.add("Bin must have a positive depth");
        }
        
        // Max weight is optional but must be positive if present
        if (binJson.has("maxWeight") && (!binJson.get("maxWeight").isNumber() || binJson.get("maxWeight").asDouble() <= 0)) {
            errors.add("Bin maxWeight must be positive if specified");
        }
    }
    
    /**
     * Validates the box types.
     * 
     * @param boxTypesJson The box types JSON array
     * @param errors List to add errors to
     */
    private void validateBoxTypes(JsonNode boxTypesJson, List<String> errors) {
        if (!boxTypesJson.isArray()) {
            errors.add("Box types must be an array");
            return;
        }
        
        for (int i = 0; i < boxTypesJson.size(); i++) {
            JsonNode boxType = boxTypesJson.get(i);
            
            if (!boxType.isObject()) {
                errors.add("Box type at index " + i + " must be a JSON object");
                continue;
            }
            
            // Check for required box type fields
            if (!boxType.has("id") || boxType.get("id").asText().trim().isEmpty()) {
                errors.add("Box type at index " + i + " must have an id");
            }
            
            if (!boxType.has("width") || !boxType.get("width").isNumber() || boxType.get("width").asDouble() <= 0) {
                errors.add("Box type at index " + i + " must have a positive width");
            }
            
            if (!boxType.has("height") || !boxType.get("height").isNumber() || boxType.get("height").asDouble() <= 0) {
                errors.add("Box type at index " + i + " must have a positive height");
            }
            
            if (!boxType.has("depth") || !boxType.get("depth").isNumber() || boxType.get("depth").asDouble() <= 0) {
                errors.add("Box type at index " + i + " must have a positive depth");
            }
            
            if (!boxType.has("weight") || !boxType.get("weight").isNumber() || boxType.get("weight").asDouble() < 0) {
                errors.add("Box type at index " + i + " must have a non-negative weight");
            }
            
            if (!boxType.has("quantity") || !boxType.get("quantity").isNumber() || boxType.get("quantity").asInt() <= 0) {
                errors.add("Box type at index " + i + " must have a positive quantity");
            }
            
            // Shape type is optional but must be valid if present
            if (boxType.has("shapeType")) {
                String shapeType = boxType.get("shapeType").asText();
                if (!shapeType.equals("Cuboid") && !shapeType.equals("Cylinder")) {
                    errors.add("Box type at index " + i + " has invalid shape type: " + shapeType);
                }
                
                // If shape type is Cylinder, diameter is required
                if (shapeType.equals("Cylinder") && (!boxType.has("diameter") || !boxType.get("diameter").isNumber() || boxType.get("diameter").asDouble() <= 0)) {
                    errors.add("Cylinder box type at index " + i + " must have a positive diameter");
                }
            }
        }
    }
    
    /**
     * Validates the algorithm settings.
     * 
     * @param settingsJson The algorithm settings JSON
     * @param errors List to add errors to
     */
    private void validateAlgorithmSettings(JsonNode settingsJson, List<String> errors) {
        if (!settingsJson.isObject()) {
            errors.add("Algorithm settings must be a JSON object");
            return;
        }
        
        // Algorithm settings are optional and can vary by algorithm type
        // For now, we just validate that it's an object
    }
}
