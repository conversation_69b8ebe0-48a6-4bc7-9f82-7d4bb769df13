package com.packprime.innovademo.repository;

import com.packprime.innovademo.model.PieceDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Spring Data JPA repository for PieceDefinition entities. */
@Repository
public interface PieceDefinitionRepository extends JpaRepository<PieceDefinition, Long> {
  // JpaRepository provides standard CRUD operations.
  // Custom query methods can be added here if needed (e.g., findByExternalId).
}
