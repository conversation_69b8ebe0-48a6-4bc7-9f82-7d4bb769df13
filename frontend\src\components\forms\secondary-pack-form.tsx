/**
 * Secondary Pack Form Component
 * This component handles input for the secondary pack (container) dimensions
 */

import React from 'react';
import { BinDto } from '@/types';
import { cn } from '@/lib/utils';

// Props for the SecondaryPackForm component
interface SecondaryPackFormProps {
  value: BinDto;
  onChange: (value: BinDto) => void;
  className?: string;
}

/**
 * Form for entering secondary pack (container) details
 */
const SecondaryPackForm: React.FC<SecondaryPackFormProps> = ({
  value,
  onChange,
  className,
}) => {
  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value: inputValue } = e.target;
    const numValue = parseFloat(inputValue);
    
    // Only update if the value is a valid number
    if (!isNaN(numValue) && numValue > 0) {
      onChange({
        ...value,
        [name]: numValue,
      });
    }
  };

  // Use a prefix for all form field IDs to avoid conflicts
  const idPrefix = "secondary-pack";

  return (
    <div className={cn("p-4 bg-white dark:bg-gray-800 rounded-lg shadow", className)}>
      <h2 className="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">Secondary Pack</h2>
      
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor={`${idPrefix}-width`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Length (mm)
            </label>
            <input
              type="number"
              id={`${idPrefix}-width`}
              name="width"
              value={value.width || ''}
              onChange={handleInputChange}
              min="1"
              step="1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter length"
            />
          </div>
          
          <div>
            <label htmlFor={`${idPrefix}-height`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Width (mm)
            </label>
            <input
              type="number"
              id={`${idPrefix}-height`}
              name="height"
              value={value.height || ''}
              onChange={handleInputChange}
              min="1"
              step="1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter width"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor={`${idPrefix}-depth`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Height (mm)
            </label>
            <input
              type="number"
              id={`${idPrefix}-depth`}
              name="depth"
              value={value.depth || ''}
              onChange={handleInputChange}
              min="1"
              step="1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter height"
            />
          </div>
          
          <div>
            <label htmlFor={`${idPrefix}-maxWeight`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Max Weight (kg)
            </label>
            <input
              type="number"
              id={`${idPrefix}-maxWeight`}
              name="maxWeight"
              value={value.maxWeight || ''}
              onChange={handleInputChange}
              min="0.1"
              step="0.1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter max weight"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecondaryPackForm;
