/**
 * Represents a rule template for packing algorithms.
 */
export interface RuleTemplate {
  id: string;
  name: string;
  description: string;
  algorithmType: string;
  source: 'builtin' | 'user';
  filename: string;
  createdAt?: string;
  lastModified?: string;
}

/**
 * Represents a bin (secondary pack) configuration.
 */
export interface Bin {
  width: number;
  height: number;
  depth: number;
  maxWeight?: number;
}

/**
 * Represents a box type (primary pack).
 */
export interface BoxType {
  id: string;
  width: number;
  height: number;
  depth: number;
  weight: number;
  quantity: number;
  shapeType?: 'Cuboid' | 'Cylinder';
  diameter?: number;
}

/**
 * Represents algorithm settings.
 */
export interface AlgorithmSettings {
  allowRotation?: boolean;
  [key: string]: any;
}

/**
 * Represents a complete packing rule.
 */
export interface PackingRule {
  name: string;
  description?: string;
  algorithmType: string;
  bin: Bin;
  boxTypes: BoxType[];
  algorithmSettings?: AlgorithmSettings;
}

/**
 * Validation result for a rule.
 */
export interface ValidationResult {
  valid: boolean;
  errors?: string[];
}
