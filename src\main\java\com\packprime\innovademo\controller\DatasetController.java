package com.packprime.innovademo.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api")
public class DatasetController {

    private static final Logger log = LoggerFactory.getLogger(DatasetController.class);
    private static final String INDUSTRY_DATA_PATH = "classpath:static/industry_data/*.json"; // Pattern to find JSON files
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Lists all available industry data files
     * @return List of filenames
     */
    @GetMapping("/list-industry-data")
    public ResponseEntity<List<Map<String, String>>> listIndustryDataFiles() {
        log.info("Received request for /api/list-industry-data");
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        List<Map<String, String>> fileInfoList = new ArrayList<>();

        try {
            Resource[] resources = resolver.getResources(INDUSTRY_DATA_PATH);
            if (resources.length == 0) {
                log.warn("No resources found matching pattern: {}", INDUSTRY_DATA_PATH);
                // Return empty list, maybe it's just empty
                return ResponseEntity.ok(fileInfoList);
            }

            for (Resource resource : resources) {
                String filename = resource.getFilename();
                if (filename != null && !filename.isEmpty()) {
                    Map<String, String> fileInfo = new HashMap<>();
                    fileInfo.put("filename", filename);
                    
                    // Try to extract name and description from the JSON file
                    try (InputStream inputStream = resource.getInputStream()) {
                        JsonNode rootNode = objectMapper.readTree(inputStream);
                        if (rootNode.has("name")) {
                            fileInfo.put("displayName", rootNode.get("name").asText());
                        } else {
                            fileInfo.put("displayName", filename.replace(".json", ""));
                        }
                        
                        if (rootNode.has("description")) {
                            fileInfo.put("description", rootNode.get("description").asText());
                        }
                    } catch (Exception e) {
                        log.warn("Could not parse JSON file {}: {}", filename, e.getMessage());
                        fileInfo.put("displayName", filename.replace(".json", ""));
                    }
                    
                    fileInfoList.add(fileInfo);
                }
            }

            log.info("Found {} industry data files.", fileInfoList.size());
            return ResponseEntity.ok(fileInfoList);

        } catch (IOException e) {
            log.error("Error accessing resources in static/industry_data: {}", e.getMessage(), e);
            // Return an internal server error response
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        } catch (Exception e) {
            log.error("An unexpected error occurred while listing industry data files: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * Gets the content of a specific industry data file
     * @param filename The name of the file to retrieve
     * @return The JSON content of the file
     */
    @GetMapping("/industry-data/{filename}")
    public ResponseEntity<Object> getIndustryDataFile(@PathVariable String filename) {
        log.info("Received request for /api/industry-data/{}", filename);
        
        if (filename == null || filename.isEmpty()) {
            return ResponseEntity.badRequest().body("Filename cannot be empty");
        }
        
        // Ensure the filename only contains valid characters
        if (!filename.matches("[a-zA-Z0-9_\\-\\.]+")) {
            return ResponseEntity.badRequest().body("Invalid filename format");
        }
        
        // Add .json extension if not present
        if (!filename.endsWith(".json")) {
            filename = filename + ".json";
        }
        
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        
        try {
            Resource resource = resolver.getResource("classpath:static/industry_data/" + filename);
            
            if (!resource.exists()) {
                log.warn("Resource not found: {}", filename);
                return ResponseEntity.notFound().build();
            }
            
            try (InputStream inputStream = resource.getInputStream()) {
                // Parse the JSON to ensure it's valid
                Object jsonContent = objectMapper.readValue(inputStream, Object.class);
                return ResponseEntity.ok(jsonContent);
            }
            
        } catch (IOException e) {
            log.error("Error reading industry data file {}: {}", filename, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error reading file: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error processing industry data file {}: {}", filename, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error: " + e.getMessage());
        }
    }
    
    /**
     * Gets metadata about the visual tests available
     * @return List of visual test information
     */
    @GetMapping("/visual-tests")
    public ResponseEntity<List<Map<String, String>>> listVisualTests() {
        log.info("Received request for /api/visual-tests");
        
        // This endpoint reuses the industry data files but formats them specifically for visual tests
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        List<Map<String, String>> testInfoList = new ArrayList<>();
        
        try {
            Resource[] resources = resolver.getResources(INDUSTRY_DATA_PATH);
            
            for (Resource resource : resources) {
                String filename = resource.getFilename();
                if (filename != null && !filename.isEmpty()) {
                    // Only include files that start with "ffdv_" for FirstFitDecreasingVolume tests
                    if (filename.startsWith("ffdv_")) {
                        Map<String, String> testInfo = new HashMap<>();
                        testInfo.put("id", filename);
                        
                        // Try to extract test name and description
                        try (InputStream inputStream = resource.getInputStream()) {
                            JsonNode rootNode = objectMapper.readTree(inputStream);
                            if (rootNode.has("name")) {
                                testInfo.put("name", rootNode.get("name").asText());
                            } else {
                                testInfo.put("name", filename.replace(".json", "").replace("_", " "));
                            }
                            
                            if (rootNode.has("description")) {
                                testInfo.put("description", rootNode.get("description").asText());
                            } else {
                                testInfo.put("description", "Visual test for " + filename);
                            }
                            
                            // Add algorithm type
                            if (rootNode.has("algorithmType")) {
                                testInfo.put("algorithmType", rootNode.get("algorithmType").asText());
                            } else {
                                testInfo.put("algorithmType", "FirstFitDecreasingVolume");
                            }
                        } catch (Exception e) {
                            log.warn("Could not parse JSON file {}: {}", filename, e.getMessage());
                            testInfo.put("name", filename.replace(".json", "").replace("_", " "));
                            testInfo.put("description", "Visual test for " + filename);
                            testInfo.put("algorithmType", "FirstFitDecreasingVolume");
                        }
                        
                        testInfoList.add(testInfo);
                    }
                }
            }
            
            return ResponseEntity.ok(testInfoList);
            
        } catch (IOException e) {
            log.error("Error accessing visual test resources: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        } catch (Exception e) {
            log.error("Unexpected error listing visual tests: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
}
