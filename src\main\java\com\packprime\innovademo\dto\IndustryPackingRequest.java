package com.packprime.innovademo.dto;

import java.util.List;

// Represents the request body for the /api/pack-industry endpoint
public class IndustryPackingRequest {
    // Use the static inner class BinDto from PackingRequest
    private PackingRequest.BinDto bin;
    private List<BoxType> boxTypes;
    private String algorithmType; // Added algorithm type field

    // Getters and Setters
    public PackingRequest.BinDto getBin() {
        return bin;
    }

    public void setBin(PackingRequest.BinDto bin) {
        this.bin = bin;
    }

    public List<BoxType> getBoxTypes() {
        return boxTypes;
    }

    public void setBoxTypes(List<BoxType> boxTypes) {
        this.boxTypes = boxTypes;
    }
    
    public String getAlgorithmType() {
        return algorithmType;
    }

    public void setAlgorithmType(String algorithmType) {
        this.algorithmType = algorithmType;
    }

    @Override
    public String toString() {
        return "IndustryPackingRequest{" +
               "bin=" + bin + // Assuming BinDto has a reasonable toString()
               ", boxTypes=" + boxTypes +
               ", algorithmType='" + algorithmType + '\'' +
               '}';
    }
}
