package com.packprime.innovademo.model;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "bin_definitions")
public class BinDefinition {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private double width;
  private double height;
  private double depth;
  private double maxWeight; // Added maxWeight constraint
  private double overhangX = 0.0; // Added overhang constraint (e.g., allowed overhang on X-axis)
  private double overhangY = 0.0; // Added overhang constraint (e.g., allowed overhang on Y-axis)

  // Assuming center of gravity limits might be complex and better handled in the algorithm/service
  // for now,
  // or represented differently (e.g., max deviation). Let's omit direct fields for now unless
  // specified further.

  // Constructors
  public BinDefinition() {}

  public BinDefinition(
      double width,
      double height,
      double depth,
      double maxWeight,
      double overhangX,
      double overhangY) {
    this.width = width;
    this.height = height;
    this.depth = depth;
    this.maxWeight = maxWeight;
    this.overhangX = overhangX;
    this.overhangY = overhangY;
  }

  // Getters and Setters
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public double getWidth() {
    return width;
  }

  public void setWidth(double width) {
    this.width = width;
  }

  public double getHeight() {
    return height;
  }

  public void setHeight(double height) {
    this.height = height;
  }

  public double getDepth() {
    return depth;
  }

  public void setDepth(double depth) {
    this.depth = depth;
  }

  public double getMaxWeight() {
    return maxWeight;
  }

  public void setMaxWeight(double maxWeight) {
    this.maxWeight = maxWeight;
  }

  public double getOverhangX() {
    return overhangX;
  }

  public void setOverhangX(double overhangX) {
    this.overhangX = overhangX;
  }

  public double getOverhangY() {
    return overhangY;
  }

  public void setOverhangY(double overhangY) {
    this.overhangY = overhangY;
  }

  // toString (optional, for debugging)
  @Override
  public String toString() {
    return "BinDefinition{"
        + "id="
        + id
        + ", width="
        + width
        + ", height="
        + height
        + ", depth="
        + depth
        + ", maxWeight="
        + maxWeight
        + ", overhangX="
        + overhangX
        + ", overhangY="
        + overhangY
        + '}';
  }
}
