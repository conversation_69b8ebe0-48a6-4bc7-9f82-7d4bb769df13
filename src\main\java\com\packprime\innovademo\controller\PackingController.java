package com.packprime.innovademo.controller;

// Removed Piece, Set, List, Collectors imports as they are no longer needed here
import com.packprime.innovademo.dto.PackingRequest;
import com.packprime.innovademo.dto.PackingResponse;
import com.packprime.innovademo.dto.IndustryPackingRequest;
import com.packprime.innovademo.dto.BoxType;
import com.packprime.innovademo.dto.AllowedVerticalOrientations;
import com.packprime.innovademo.dto.PackingRequest.PieceDto; // Import inner class
import com.packprime.innovademo.service.PackingService;
import java.util.ArrayList; // Added for list creation
import java.util.List; // Keep List for error response construction
import org.slf4j.Logger; // Added logger
import org.slf4j.LoggerFactory; // Added logger
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/packing")
public class PackingController {

  private static final Logger logger =
      LoggerFactory.getLogger(PackingController.class); // Added logger
  private final PackingService packingService;

  @Autowired
  public PackingController(PackingService packingService) {
    this.packingService = packingService;
  }

  @PostMapping("/pack")
  public ResponseEntity<PackingResponse> packItems(@RequestBody PackingRequest request) {
    logger.info("Received /pack request");
    try {
      // Basic validation (could be enhanced with @Valid annotation and validation constraints on
      // DTO)
      if (request == null
          || request.getBin() == null
          || request.getPieces() == null
          || request.getPieces().isEmpty()) {
        logger.warn("Bad request received: Missing bin or pieces.");
        // Consider returning a structured error response
        return ResponseEntity.badRequest().body(null);
      }

      // Process cylinder shapes - map to cuboids for the algorithm
      processCylinderShapes(request);

      // Log the request to see what's being received by the backend
      logger.info("Request pieces: {}", request.getPieces());

      // 1. Call the packing service - it now returns the complete PackingResponse
      PackingResponse response = packingService.performPacking(request);
      
      // Log the response to see what's being sent back
      logger.info("Response pieces: {}", response.getPieces());
      
      logger.info(
          "Packing successful. Algorithm: {}, Placed: {}, Unplaced: {}",
          response.getAlgorithmUsed(),
          response.getPlacedPieces().size(),
          response.getUnplacedPieceIds().size());

      // 2. Return the response from the service directly
      return ResponseEntity.ok(response);

    } catch (IllegalArgumentException e) {
      // Handle known invalid input arguments (e.g., negative dimensions)
      logger.error("Invalid input argument during packing: {}", e.getMessage());
      // Return a structured error response DTO if desired
      return ResponseEntity.badRequest()
          .body(
              new PackingResponse( // Corrected constructor call
                  null, // placedPieces
                  null, // unplacedPieceIds
                  0.0, // totalWeight
                  0.0, // volumeUtilization
                  null, // algorithmUsed (Error case)
                  List.of("Invalid input: " + e.getMessage()), // statusMessages
                  null // pieces
              )); // statusMessages
    } catch (IllegalStateException e) {
      // Handle internal configuration errors (e.g., algorithm not found)
      logger.error("Internal state error during packing: {}", e.getMessage());
      return ResponseEntity.internalServerError()
          .body(
              new PackingResponse( // Corrected constructor call
                  null, // placedPieces
                  null, // unplacedPieceIds
                  0.0, // totalWeight
                  0.0, // volumeUtilization
                  null, // algorithmUsed (Error case)
                  List.of("Internal server error: " + e.getMessage()), // statusMessages
                  null // pieces
              )); // statusMessages
    } catch (Exception e) {
      // Log unexpected exceptions
      logger.error("Unexpected error during packing: {}", e.getMessage(), e); // Log stack trace
      return ResponseEntity.internalServerError()
          .body(
              new PackingResponse( // Corrected constructor call
                  null, // placedPieces
                  null, // unplacedPieceIds
                  0.0, // totalWeight
                  0.0, // volumeUtilization
                  null, // algorithmUsed (Error case)
                  List.of("An unexpected error occurred during packing."), // statusMessages
                  null // pieces
              )); // statusMessages
    }
  }

  @PostMapping("/pack-industry")
  public ResponseEntity<PackingResponse> packIndustryItems(@RequestBody IndustryPackingRequest industryRequest) {
    logger.info("Received /pack-industry request");
    try {
      // Basic validation
      if (industryRequest == null || industryRequest.getBin() == null || industryRequest.getBoxTypes() == null || industryRequest.getBoxTypes().isEmpty()) {
        logger.warn("Bad request received for /pack-industry: Missing bin or boxTypes.");
        return ResponseEntity.badRequest().body(null); // Or a structured error
      }

      // 1. Expand BoxTypes into PieceDto list
      List<PieceDto> expandedPieces = new ArrayList<>();
      for (BoxType boxType : industryRequest.getBoxTypes()) {
        if (boxType.getQuantity() <= 0) {
          logger.warn("Skipping box type {} with zero or negative quantity {}", boxType.getId(), boxType.getQuantity());
          continue; // Skip types with no quantity
        }
        AllowedVerticalOrientations avo = boxType.getAllowedVerticalOrientations();
        // Assume default false if avo is null
        boolean allowRotX = avo != null && avo.isDepth();
        boolean allowRotY = avo != null && avo.isWidth();
        boolean allowRotZ = avo != null && avo.isHeight();

        for (int i = 0; i < boxType.getQuantity(); i++) {
          PieceDto piece = new PieceDto();
          // Generate a unique ID for each instance
          piece.setId(boxType.getId() + "_instance_" + (i + 1));
          
          // Handle shape type and dimensions
          piece.setShapeType(boxType.getShapeType() != null ? boxType.getShapeType() : "Cuboid");
          
          if ("Cylinder".equals(boxType.getShapeType()) && boxType.getDiameter() != null) {
            // For cylinders, store the diameter
            piece.setDiameter(boxType.getDiameter());
            // Width and height will be set in processCylinderShapes
            piece.setWidth(boxType.getWidth());
            piece.setHeight(boxType.getHeight());
          } else {
            // For cuboids, use the standard dimensions
            piece.setWidth(boxType.getWidth());
            piece.setHeight(boxType.getHeight());
          }
          
          piece.setDepth(boxType.getDepth());
          piece.setWeight(boxType.getWeight());
          // Map orientation constraints (check assumption mentioned above)
          piece.setAllowRotationX(allowRotX);
          piece.setAllowRotationY(allowRotY);
          piece.setAllowRotationZ(allowRotZ);
          // Set defaults for other PieceDto fields if needed (e.g., value, stackingLimit)
          piece.setValue(0.0); // Default value
          // piece.setStackingLimit(null);
          // piece.setLoadPriority(null);

          expandedPieces.add(piece);
        }
      }

      if (expandedPieces.isEmpty()) {
         logger.warn("No pieces generated from box types for /pack-industry request.");
         // Return an empty successful response or bad request depending on requirements
         return ResponseEntity.badRequest().body(new PackingResponse(null, null, 0.0, 0.0, "N/A", List.of("No valid pieces to pack"), null));
      }

      // 2. Create a standard PackingRequest
      PackingRequest standardRequest = new PackingRequest();
      standardRequest.setBin(industryRequest.getBin());
      standardRequest.setPieces(expandedPieces);
      standardRequest.setAlgorithmType("Default"); // Use default or make configurable if needed
      standardRequest.setAlgorithmSettings(null); // No specific settings for now

      // Process cylinder shapes if any
      processCylinderShapes(standardRequest);

      // 3. Call the packing service with the standard request
      PackingResponse response = packingService.performPacking(standardRequest);
      
      logger.info("Packing successful for /pack-industry. Algorithm: {}, Placed: {}, Unplaced: {}",
                  response.getAlgorithmUsed(),
                  response.getPlacedPieces() != null ? response.getPlacedPieces().size() : 0,
                  response.getUnplacedPieceIds() != null ? response.getUnplacedPieceIds().size() : 0);

      // 4. Return the response
      return ResponseEntity.ok(response);

    } catch (IllegalArgumentException e) {
      logger.error("Invalid input argument during /pack-industry: {}", e.getMessage());
      return ResponseEntity.badRequest().body(new PackingResponse(null, null, 0.0, 0.0, null, List.of("Invalid input: " + e.getMessage()), null));
    } catch (IllegalStateException e) {
      logger.error("Internal state error during /pack-industry: {}", e.getMessage());
      return ResponseEntity.internalServerError().body(new PackingResponse(null, null, 0.0, 0.0, null, List.of("Internal server error: " + e.getMessage()), null));
    } catch (Exception e) {
      logger.error("Unexpected error during /pack-industry: {}", e.getMessage(), e);
      return ResponseEntity.internalServerError().body(new PackingResponse(null, null, 0.0, 0.0, null, List.of("An unexpected error occurred during packing."), null));
    }
  }

  /**
   * Process cylinder shapes by mapping them to cuboids for the packing algorithm.
   * For cylinders, we set width and height equal to the diameter.
   */
  private void processCylinderShapes(PackingRequest request) {
    if (request.getPieces() == null) return;
    
    for (PieceDto piece : request.getPieces()) {
      if ("Cylinder".equals(piece.getShapeType()) && piece.getDiameter() != null) {
        logger.info("Processing cylinder shape for piece {}: diameter={}", piece.getId(), piece.getDiameter());
        
        // For cylinders, set width and height equal to the diameter
        piece.setWidth(piece.getDiameter());
        piece.setHeight(piece.getDiameter());
        
        // For cylinders, only allow rotation around Z axis (height)
        piece.setAllowRotationX(false);
        piece.setAllowRotationY(false);
      }
    }
  }
}
