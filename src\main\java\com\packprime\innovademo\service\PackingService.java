package com.packprime.innovademo.service;

import com.packprime.innovademo.algorithm.Bin;
import com.packprime.innovademo.algorithm.PackingAlgorithm;
import com.packprime.innovademo.algorithm.PackingResult;
import com.packprime.innovademo.algorithm.Piece;
import com.packprime.innovademo.algorithm.Position;
import com.packprime.innovademo.dto.PackingRequest;
import com.packprime.innovademo.dto.PackingResponse;
import com.packprime.innovademo.dto.PackingResponse.PositionDto;
import com.packprime.innovademo.model.BinDefinition;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger; // Added logger
import org.slf4j.LoggerFactory; // Added logger
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PackingService {

  private static final Logger logger =
      LoggerFactory.getLogger(PackingService.class); // Added logger
  private final Map<String, PackingAlgorithm> algorithms;

  @Autowired
  public PackingService(Map<String, PackingAlgorithm> algorithms) {
    this.algorithms = algorithms;
  }

  /**
   * Performs packing based on the provided request DTO using the specified algorithm.
   *
   * @param request The packing request containing bin and piece details with constraints.
   * @return A PackingResponse containing the results of the packing operation.
   */
  public PackingResponse performPacking(PackingRequest request) { // Changed return type
    long startTime = System.currentTimeMillis();
    logger.info("Received packing request. Algorithm: {}", request.getAlgorithmType());

    // 1. Create BinDefinition from DTO
    PackingRequest.BinDto binDto = request.getBin();
    if (binDto == null) {
      throw new IllegalArgumentException("Bin definition is missing in the request.");
    }
    BinDefinition binDefinition =
        new BinDefinition( // Assuming a constructor or use setters
            binDto.getWidth(),
            binDto.getHeight(),
            binDto.getDepth(),
            Optional.ofNullable(binDto.getMaxWeight()).orElse(Double.MAX_VALUE), // Default if null
            Optional.ofNullable(binDto.getOverhangX()).orElse(0.0), // Default if null
            Optional.ofNullable(binDto.getOverhangY()).orElse(0.0) // Default if null
            );
    logger.debug("BinDefinition created: {}", binDefinition);

    // 2. Create Bin (using only dimensions now)
    Bin bin =
        new Bin(binDefinition.getWidth(), binDefinition.getHeight(), binDefinition.getDepth());
    logger.debug("Bin created: W={}, H={}, D={}", bin.getWidth(), bin.getHeight(), bin.getDepth());

    // 3. Create Pieces from DTOs
    List<Piece> pieces =
        request.getPieces().stream()
            .map(
                dto ->
                    new Piece(
                        dto.getId() != null
                            ? dto.getId()
                            : "auto_" + System.nanoTime(), // Generate ID if null
                        dto.getWidth(),
                        dto.getHeight(),
                        dto.getDepth(),
                        dto.getValue(),
                        dto.getWeight(),
                        Optional.ofNullable(dto.getAllowRotationX()).orElse(true),
                        Optional.ofNullable(dto.getAllowRotationY()).orElse(true),
                        Optional.ofNullable(dto.getAllowRotationZ()).orElse(true),
                        Optional.ofNullable(dto.getStackingLimit()).orElse(Integer.MAX_VALUE),
                        Optional.ofNullable(dto.getLoadPriority()).orElse(0),
                        Optional.ofNullable(dto.getMinQuantity()).orElse(0),
                        Optional.ofNullable(dto.getMaxQuantity()).orElse(Integer.MAX_VALUE)))
            .collect(Collectors.toList());
    logger.debug("Converted {} DTOs to Piece objects.", pieces.size());

    // 4. Get Algorithm Settings DTO
    PackingRequest.AlgorithmSettingsDto settings =
        Optional.ofNullable(request.getAlgorithmSettings())
            .orElse(new PackingRequest.AlgorithmSettingsDto()); // Provide default if null
    logger.debug("Using Algorithm Settings: {}", settings);

    // 5. Select Algorithm
    String algorithmType = Optional.ofNullable(request.getAlgorithmType()).orElse("Default");
    PackingAlgorithm algorithm = algorithms.get(algorithmType);
    String actualAlgorithmUsed = algorithmType; // Track the algorithm actually used

    if (algorithm == null) {
      logger.warn("Unknown algorithm type '{}'. Falling back to Default.", algorithmType);
      algorithm = algorithms.get("Default");
      actualAlgorithmUsed = "Default"; // Update the tracked algorithm
      if (algorithm == null) {
        logger.error("Default packing algorithm not found. Cannot proceed.");
        // Consider throwing a more specific exception or returning an error response
        throw new IllegalStateException("Default packing algorithm not found.");
      }
    }
    logger.info("Selected algorithm: {}", actualAlgorithmUsed);

    // 6. Run the selected packing algorithm (using new signature)
    PackingResult result = algorithm.pack(bin, pieces, binDefinition, settings);
    logger.debug(
        "Packing finished. Placed: {}, Unplaced: {}",
        result.getPlacedPieces().size(),
        result.getUnplacedPieces().size());

    // 7. Process the PackingResult
    List<PackingResponse.PlacedPieceDto> placedPieceDtos =
        result.getPlacedPieces().stream()
            .map(
                p -> {
                  Piece.Dimensions dims = p.getDimensionsForOrientation(p.getOrientation());
                  Position pos = p.getPosition();
                  // Ensure constructor arguments match PlacedPieceDto signature
                  return new PackingResponse.PlacedPieceDto(
                      p.getId(),
                      p.getOriginalWidth(), // Pass original dimensions
                      p.getOriginalHeight(),
                      p.getOriginalDepth(),
                      dims.w, // Pass placed dimensions
                      dims.h,
                      dims.d,
                      p.getWeight(), // Pass weight
                      new PositionDto(pos.getX(), pos.getY(), pos.getZ()), // Create PositionDto
                      p.getOrientation()); // Pass orientation
                })
            .collect(Collectors.toList());

    List<String> unplacedPieceIds =
        result.getUnplacedPieces().stream().map(Piece::getId).collect(Collectors.toList());

    double totalPlacedWeight =
        result.getPlacedPieces().stream().mapToDouble(Piece::getWeight).sum();
    double totalPlacedVolume =
        result.getPlacedPieces().stream().mapToDouble(Piece::getVolume).sum();
    double volumeUtilization =
        (bin.getVolume() > 1e-9)
            ? (totalPlacedVolume / bin.getVolume()) * 100.0
            : 0.0; // Avoid division by zero

    long duration = System.currentTimeMillis() - startTime;
    logger.info(
        "Packing complete in {} ms. Volume Util: {:.2f}%, Total Weight: {:.2f}",
        duration, volumeUtilization, totalPlacedWeight);

    // 8. Construct and return the PackingResponse (ensure order matches constructor)
    return new PackingResponse(
        placedPieceDtos, // List<PlacedPieceDto> placedPieces
        unplacedPieceIds, // List<String> unplacedPieceIds
        totalPlacedWeight, // double totalWeight
        volumeUtilization, // double volumeUtilization
        actualAlgorithmUsed, // String algorithmUsed
        result.getStatusMessages(), // List<String> statusMessages
        request.getPieces() // Include original pieces for visualization
    );
  }
}
