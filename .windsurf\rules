- Always use ShadCN components where applicable.
- Use Context7 MCP Tool to find applicable components and packages if you don't knowabout one. You can also use this to know about a library or a package.
- You can also use <PERSON><PERSON> and <PERSON>tch MCP Tools for something that Context7 can't satisfy.
- If the user provides a URL for a component or package then use the fetch tool to scrapeit and use that.
- Maintain a list of used ShadCN components in ShadCN-context.md (make one if it doesn't
exist). It should contain all the components used and reference this to check if you need
to install a ShadCN component from scratch with the CLI tool or not