package com.packprime.innovademo.algorithm;

import com.packprime.innovademo.dto.PackingRequest; // Added import
import com.packprime.innovademo.model.BinDefinition; // Added import
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger; // Added logger
import org.slf4j.LoggerFactory; // Added logger

/** Represents the container (bin) into which pieces are packed. */
public class Bin {
  private static final Logger logger = LoggerFactory.getLogger(Bin.class); // Added logger

  private final double width;
  private final double height;
  private final double depth;
  private final double volume;
  private double currentWeight; // Tracks the total weight of pieces placed in the bin.

  private List<Piece> placedPieces; // List of pieces successfully placed.
  private Set<Position> corners; // Set of potential corners where the next piece might be placed.

  // Epsilon for floating point comparisons to handle precision issues.
  private static final double EPSILON = 1e-6;

  /**
   * Constructs a new Bin with the specified dimensions.
   *
   * @param width The width of the bin. Must be positive.
   * @param height The height of the bin. Must be positive.
   * @param depth The depth of the bin. Must be positive.
   * @throws IllegalArgumentException if any dimension is non-positive.
   */
  public Bin(double width, double height, double depth) {
    if (width <= 0 || height <= 0 || depth <= 0) {
      throw new IllegalArgumentException("Bin dimensions must be positive.");
    }
    this.width = width;
    this.height = height;
    this.depth = depth;
    this.volume = width * height * depth;
    this.currentWeight = 0.0;
    this.placedPieces = new ArrayList<>();
    this.corners = new HashSet<>();
    this.corners.add(new Position(0, 0, 0)); // Start with the origin corner
  }

  /** Resets the bin to its initial empty state. */
  public void reset() {
    this.placedPieces.clear();
    this.corners.clear();
    this.corners.add(new Position(0, 0, 0));
    this.currentWeight = 0.0;
  }

  /**
   * Checks if a piece can be placed at a given corner with a specific orientation, considering
   * various constraints: bin boundaries (including overhang), max weight, overlaps with other
   * pieces, stability (support from below), and stacking limits.
   *
   * @param piece The piece to check.
   * @param corner The potential placement corner (bottom-front-left of the piece).
   * @param orientation The orientation of the piece (0-5).
   * @param binDefinition Contains bin-specific constraints like maxWeight and overhang allowances.
   * @param settings Contains algorithm-level settings (e.g., allowMixedLayers - currently TODO).
   * @param statusMessages List to add violation messages to (e.g., "Exceeds max weight").
   * @return true if the placement is valid according to all checked constraints, false otherwise.
   */
  public boolean checkPlacement(
      Piece piece,
      Position corner,
      int orientation,
      BinDefinition binDefinition,
      PackingRequest.AlgorithmSettingsDto settings,
      List<String> statusMessages) {
    // 0. Check Piece Orientation Constraint
    // Assumes Piece object holds its allowed rotation flags from PieceDefinition
    if (!piece.isOrientationAllowed(orientation)) {
      statusMessages.add(
          String.format(
              "Placement failed for piece %s: Orientation %d is not allowed for this piece type.",
              piece.getId(), orientation));
      return false;
    }

    Piece.Dimensions dims = piece.getDimensionsForOrientation(orientation);

    // 1. Check Bin Boundaries (considering overhang from BinDefinition)
    double allowedWidth = this.width + binDefinition.getOverhangX();
    double allowedHeight = this.height; // Height constraint is strict (no vertical overhang)
    double allowedDepth = this.depth + binDefinition.getOverhangY();

    if (corner.getX() + dims.w > allowedWidth + EPSILON) {
      statusMessages.add(
          String.format(
              "Placement failed for piece %s: Exceeds allowed width (%f > %f)",
              piece.getId(), corner.getX() + dims.w, allowedWidth));
      return false;
    }
    if (corner.getY() + dims.h > allowedHeight + EPSILON) {
      statusMessages.add(
          String.format(
              "Placement failed for piece %s: Exceeds allowed height (%f > %f)",
              piece.getId(), corner.getY() + dims.h, allowedHeight));
      return false;
    }
    if (corner.getZ() + dims.d > allowedDepth + EPSILON) {
      statusMessages.add(
          String.format(
              "Placement failed for piece %s: Exceeds allowed depth (%f > %f)",
              piece.getId(), corner.getZ() + dims.d, allowedDepth));
      return false;
    }

    // 2. Check Bin Max Weight Constraint
    if (this.currentWeight + piece.getWeight() > binDefinition.getMaxWeight() + EPSILON) {
      statusMessages.add(
          String.format(
              "Placement failed for piece %s: Exceeds bin max weight (Current: %.2f, Piece: %.2f,"
                  + " Max: %.2f)",
              piece.getId(), this.currentWeight, piece.getWeight(), binDefinition.getMaxWeight()));
      return false;
    }

    // 3. Check for Overlaps with other placed pieces
    Box pieceBox = new Box(corner, dims);
    for (Piece placed : placedPieces) {
      Piece.Dimensions placedDims = placed.getDimensionsForOrientation(placed.getOrientation());
      Box placedBox = new Box(placed.getPosition(), placedDims);
      if (boxesOverlap(pieceBox, placedBox)) {
        statusMessages.add(
            String.format(
                "Placement failed for piece %s: Overlaps with already placed piece %s",
                piece.getId(), placed.getId()));
        return false;
      }
    }

    // 4. Check Support, Stacking, and Load Priority Constraints (only if not on bin floor)
    if (corner.getY() > EPSILON) {
      // Pass binDefinition and settings to the check method
      if (!checkSupportAndStacking(piece, pieceBox, binDefinition, settings, statusMessages)) {
        // Violation message added within checkSupportAndStacking
        return false;
      }
    }

    // 5. Check General Algorithm Settings (Placeholder)
    // TODO: Implement checks based on settings like allowMixedColumns, allowMixedLayers.
    //       This requires checking adjacent/supporting pieces' types/IDs.
    // if (settings != null) {
    //    if (!settings.isAllowMixedLayers() && corner.getY() > EPSILON) { ... check pieces below
    // ... }
    //    if (!settings.isAllowMixedColumns()) { ... check pieces adjacent ... }
    // }
    // if (!checkGeneralSettings(piece, pieceBox, settings, statusMessages)) {
    //     return false;
    // }

    // If all checks pass
    return true;
  }

  /**
   * Checks if a piece placement is adequately supported by pieces below it and respects stacking
   * limits. This involves: 1. Finding all pieces directly below the potential placement area. 2.
   * Checking if any supporting piece forbids further stacking (stackingLimit <= 0). 3. Performing a
   * basic stability check: ensuring all 4 bottom corners of the piece being placed are supported by
   * *some* piece below.
   *
   * @param piece The piece being placed.
   * @param pieceBox The bounding box of the piece being placed.
   * @param statusMessages List to add violation messages to.
   * @param binDefinition Bin constraints (currently unused here but passed for consistency).
   * @param settings Algorithm settings (e.g., for mixed layer checks - currently TODO).
   * @return true if the placement is supported and respects stacking/priority limits, false
   *     otherwise.
   */
  private boolean checkSupportAndStacking(
      Piece piece,
      Box pieceBox,
      BinDefinition binDefinition, // Added for potential future use
      PackingRequest.AlgorithmSettingsDto settings, // Added for mixed layer check
      List<String> statusMessages) {
    // Flags to track if each bottom corner of the pieceBox is supported.
    boolean corner1Supported = false; // Bottom-front-left (minX, minY, minZ)
    boolean corner2Supported = false; // Bottom-front-right (maxX, minY, minZ)
    boolean corner3Supported = false; // Bottom-back-left (minX, minY, maxZ)
    boolean corner4Supported = false; // Bottom-back-right (maxX, minY, maxZ)
    boolean foundSupportingPiece = false; // Track if any piece is below

    for (Piece placedPiece : placedPieces) {
      Piece.Dimensions placedDims =
          placedPiece.getDimensionsForOrientation(placedPiece.getOrientation());
      Box placedBox = new Box(placedPiece.getPosition(), placedDims);

      // Check if the top face of placedBox is directly below the bottom face of pieceBox
      boolean isDirectlyBelow = Math.abs(placedBox.maxY - pieceBox.minY) < EPSILON;

      if (isDirectlyBelow) {
        // Check for overlap in the XZ plane (footprint overlap) - inclusive check
        boolean overlapXZ =
            (pieceBox.maxX >= placedBox.minX - EPSILON && pieceBox.minX <= placedBox.maxX + EPSILON)
                && (pieceBox.maxZ >= placedBox.minZ - EPSILON
                    && pieceBox.minZ <= placedBox.maxZ + EPSILON);

        if (overlapXZ) {
          foundSupportingPiece = true; // Mark that we found a piece below
          // This placedPiece is potentially supporting the new piece (even if just touching edges).

          // Check Load Priority: Higher priority cannot be placed on lower priority.
          if (piece.getLoadPriority() > placedPiece.getLoadPriority()) {
            statusMessages.add(
                String.format(
                    "Placement failed for piece %s (Priority: %d): Cannot place on lower priority"
                        + " piece %s (Priority: %d).",
                    piece.getId(),
                    piece.getLoadPriority(),
                    placedPiece.getId(),
                    placedPiece.getLoadPriority()));
            return false;
          }

          // Check Stacking Limit of the supporting piece.
          // A limit of 0 means nothing can be placed on it.
          // A limit > 0 means that many items can be placed ON TOP.
          if (placedPiece.getStackingLimit() <= placedPiece.getPiecesStackedOnTopCount()) {
            statusMessages.add(
                String.format(
                    "Placement failed for piece %s: Supporting piece %s has reached its stacking"
                        + " limit (Limit: %d, Current Stack: %d).",
                    piece.getId(),
                    placedPiece.getId(),
                    placedPiece.getStackingLimit(),
                    placedPiece.getPiecesStackedOnTopCount()));
            return false;
          }

          // Check if this placedBox supports the corners of the pieceBox's base for stability.
          if (isPointSupported(pieceBox.minX, pieceBox.minZ, placedBox)) corner1Supported = true;
          if (isPointSupported(pieceBox.maxX, pieceBox.minZ, placedBox)) corner2Supported = true;
          if (isPointSupported(pieceBox.minX, pieceBox.maxZ, placedBox)) corner3Supported = true;
          if (isPointSupported(pieceBox.maxX, pieceBox.maxZ, placedBox)) corner4Supported = true;
        }
      }
    }

    // Stability Check: Only apply if there was at least one piece below the placement area.
    // Ensure all 4 bottom corners are supported by at least one piece below.
    if (foundSupportingPiece
        && !(corner1Supported && corner2Supported && corner3Supported && corner4Supported)) {
      statusMessages.add(
          String.format(
              "Placement failed for piece %s: Unstable - not all bottom corners are supported by"
                  + " pieces below.",
              piece.getId()));
      return false;
    }

    return true; // Placement is supported and respects basic stacking/priority limits.
  }

  /**
   * Checks if an XZ point (representing a corner of the piece being placed) lies within or on the
   * boundary of the XZ projection (footprint) of a supporting box below it. Used for the stability
   * check.
   *
   * @param px X coordinate of the point to check.
   * @param pz Z coordinate of the point to check.
   * @param supportingBox The potential supporting box below.
   * @return true if the point's XZ coordinates are within or on the boundary of the supportingBox's
   *     XZ footprint.
   */
  private boolean isPointSupported(double px, double pz, Box supportingBox) {
    // Check if px is within [minX, maxX] and pz is within [minZ, maxZ] of the supporting box
    return px >= supportingBox.minX - EPSILON
        && px <= supportingBox.maxX + EPSILON
        && pz >= supportingBox.minZ - EPSILON
        && pz <= supportingBox.maxZ + EPSILON;
  }

  /**
   * Places a piece in the bin at the specified corner and orientation. Updates the list of placed
   * pieces, updates the current weight, and recalculates available corners based on the new
   * placement. Assumes {@link #checkPlacement} was called and returned true before calling this.
   *
   * @param piece The piece to place. Its position, orientation, and placed status will be updated.
   * @param corner The corner where the piece's bottom-front-left is placed.
   * @param orientation The orientation (0-5) of the placed piece.
   */
  public void placePiece(Piece piece, Position corner, int orientation) {
    Piece.Dimensions dims = piece.getDimensionsForOrientation(orientation);

    // Update piece state
    piece.setPosition(corner);
    piece.setDimensionsForOrientation(
        orientation); // Update piece's internal dimensions/orientation
    piece.setPlaced(true);
    this.placedPieces.add(piece);
    this.currentWeight += piece.getWeight();

    // --- Update Stacking Counts on Supporting Pieces ---
    // Find pieces directly below the newly placed piece and increment their stack count.
    Box newPieceBox = new Box(corner, dims);
    for (Piece supportingPiece : findSupportingPieces(newPieceBox)) {
      supportingPiece.incrementPiecesStackedOnTopCount();
      logger.trace(
          "Incremented stack count for supporting piece {}. New count: {}",
          supportingPiece.getId(),
          supportingPiece.getPiecesStackedOnTopCount());
    }

    // --- Corner Management ---
    // Remove the corner used for placement
    this.corners.remove(corner);

    // Add new potential corners created by placing the piece at its top-front-left,
    // bottom-back-left, and bottom-front-right edges relative to the placement corner.
    // Only add corners if they are within the strict bin boundaries (excluding overhang).
    Position cornerX = new Position(corner.getX() + dims.w, corner.getY(), corner.getZ());
    Position cornerY = new Position(corner.getX(), corner.getY() + dims.h, corner.getZ());
    Position cornerZ = new Position(corner.getX(), corner.getY(), corner.getZ() + dims.d);

    // Add only if the new corner is strictly within the bin's physical dimensions
    if (cornerX.getX() < this.width - EPSILON) addCornerIfNotExists(cornerX);
    if (cornerY.getY() < this.height - EPSILON) addCornerIfNotExists(cornerY);
    if (cornerZ.getZ() < this.depth - EPSILON) addCornerIfNotExists(cornerZ);

    // Remove corners that are now strictly inside the newly placed piece
    Box placedBox = new Box(corner, dims);
    this.corners =
        this.corners.stream()
            .filter(c -> !isPointInsideBox(c, placedBox)) // Check if strictly inside
            .collect(Collectors.toSet());
  }

  /**
   * Finds all placed pieces that are directly underneath the given box (potential placement). Used
   * to update stacking counts.
   *
   * @param placedBox The box representing the piece that was just placed.
   * @return A list of pieces directly supporting the placedBox.
   */
  private List<Piece> findSupportingPieces(Box placedBox) {
    List<Piece> supportingPieces = new ArrayList<>();
    Position placedBoxCorner =
        new Position(
            placedBox.minX,
            placedBox.minY,
            placedBox.minZ); // Create Position object for comparison
    for (Piece potentialSupporter : this.placedPieces) {
      // Skip the piece itself if it's somehow in the list already (shouldn't happen here)
      // Compare the potential supporter's position with the corner of the newly placed box
      if (potentialSupporter.getPosition().equals(placedBoxCorner)) {
        continue;
      }

      Piece.Dimensions supporterDims =
          potentialSupporter.getDimensionsForOrientation(potentialSupporter.getOrientation());
      Box supporterBox = new Box(potentialSupporter.getPosition(), supporterDims);

      // Check if the top face of supporterBox is directly below the bottom face of placedBox
      boolean isDirectlyBelow = Math.abs(supporterBox.maxY - placedBox.minY) < EPSILON;

      if (isDirectlyBelow) {
        // Check for overlap in the XZ plane (footprint overlap)
        boolean overlapXZ =
            (placedBox.maxX > supporterBox.minX + EPSILON
                    && placedBox.minX < supporterBox.maxX - EPSILON)
                && (placedBox.maxZ > supporterBox.minZ + EPSILON
                    && placedBox.minZ < supporterBox.maxZ - EPSILON);

        if (overlapXZ) {
          supportingPieces.add(potentialSupporter);
        }
      }
    }
    return supportingPieces;
  }

  /**
   * Adds a new corner to the set if it doesn't already exist. Uses the `Position` class's `equals`
   * and `hashCode` for uniqueness.
   *
   * @param newCorner The potential new corner.
   */
  private void addCornerIfNotExists(Position newCorner) {
    // HashSet automatically handles uniqueness based on Position's equals/hashCode
    this.corners.add(newCorner);
  }

  /**
   * Checks if two 3D boxes overlap significantly (more than just touching faces). Uses EPSILON for
   * floating point comparisons.
   *
   * @param box1 The first box.
   * @param box2 The second box.
   * @return true if they overlap volumetrically, false otherwise.
   */
  private boolean boxesOverlap(Box box1, Box box2) {
    // Check for non-overlap in each dimension
    boolean noOverlapX = box1.maxX <= box2.minX + EPSILON || box1.minX >= box2.maxX - EPSILON;
    boolean noOverlapY = box1.maxY <= box2.minY + EPSILON || box1.minY >= box2.maxY - EPSILON;
    boolean noOverlapZ = box1.maxZ <= box2.minZ + EPSILON || box1.minZ >= box2.maxZ - EPSILON;

    // If there is no overlap in any dimension, the boxes do not overlap
    return !(noOverlapX || noOverlapY || noOverlapZ);
  }

  /**
   * Checks if a point is strictly inside a box (not on the boundaries). Used for removing corners
   * covered by a newly placed piece in older implementations, potentially less robust than {@link
   * #isPointInsideOrOnBoundary}.
   *
   * @param point The point to check.
   * @param box The box to check against.
   * @return true if the point is strictly inside the box, false otherwise.
   */
  private boolean isPointInsideBox(Position point, Box box) {
    return point.getX() > box.minX + EPSILON
        && point.getX() < box.maxX - EPSILON
        && point.getY() > box.minY + EPSILON
        && point.getY() < box.maxY - EPSILON
        && point.getZ() > box.minZ + EPSILON
        && point.getZ() < box.maxZ - EPSILON;
  }

  /**
   * Checks if a point is inside or on the boundary of a box. Used for more robust corner removal in
   * placePiece.
   *
   * @param point The point to check.
   * @param box The box to check against.
   * @return true if the point is inside or on the boundary, false otherwise.
   */
  private boolean isPointInsideOrOnBoundary(Position point, Box box) {
    return point.getX() >= box.minX - EPSILON
        && point.getX() <= box.maxX + EPSILON
        && point.getY() >= box.minY - EPSILON
        && point.getY() <= box.maxY + EPSILON
        && point.getZ() >= box.minZ - EPSILON
        && point.getZ() <= box.maxZ + EPSILON;
  }

  // --- Getters ---
  public double getWidth() {
    return width;
  }

  public double getHeight() {
    return height;
  }

  public double getDepth() {
    return depth;
  }

  public double getVolume() {
    return volume;
  }

  public List<Piece> getPlacedPieces() {
    return placedPieces;
  }

  public Set<Position> getCorners() {
    return corners;
  }

  // Removed getMaxWeight() as it's now in BinDefinition
  // public double getMaxWeight() {
  //     return maxWeight;
  // }

  public double getCurrentWeight() {
    return currentWeight;
  }

  public double getVolumeUtilisation() {
    double loadedVolume = 0;
    for (Piece piece : placedPieces) {
      loadedVolume += piece.getVolume();
    }
    double binVolume = this.width * this.height * this.depth;
    if (binVolume == 0) {
      return 0.0; // Avoid division by zero if bin has no volume
    }
    double utilisation = (loadedVolume / binVolume) * 100.0;

    // Clamp the value between 0 and 100, though ideally it should never exceed 100
    return Math.max(0.0, Math.min(utilisation, 100.0));
  }

  /** Helper class representing an axis-aligned bounding box (AABB). */
  private static class Box {
    final double minX, minY, minZ;
    final double maxX, maxY, maxZ;

    /**
     * Creates a Box from a corner position and piece dimensions.
     *
     * @param corner The minimum coordinate corner (bottom-front-left).
     * @param dims The dimensions (width, height, depth) of the box.
     */
    Box(Position corner, Piece.Dimensions dims) {
      this.minX = corner.getX();
      this.minY = corner.getY();
      this.minZ = corner.getZ();
      this.maxX = corner.getX() + dims.w;
      this.maxY = corner.getY() + dims.h;
      this.maxZ = corner.getZ() + dims.d;
    }
  }
}
