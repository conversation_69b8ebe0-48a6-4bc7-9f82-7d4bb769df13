package com.packprime.innovademo;

import static org.junit.jupiter.api.Assertions.*;

import com.packprime.innovademo.algorithm.Bin;
import com.packprime.innovademo.algorithm.DefaultPackingAlgorithm;
import com.packprime.innovademo.algorithm.PackingAlgorithm;
import com.packprime.innovademo.algorithm.PackingResult;
import com.packprime.innovademo.algorithm.Piece;
import com.packprime.innovademo.dto.PackingRequest; // Needed for AlgorithmSettingsDto
import com.packprime.innovademo.model.BinDefinition;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class DefaultPackingAlgorithmTest {

  // Helper to create a BinDefinition for tests
  private BinDefinition createDefaultBinDef(double maxWeight) {
    BinDefinition binDef = new BinDefinition();
    binDef.setMaxWeight(maxWeight);
    binDef.setOverhangX(0);
    binDef.setOverhangY(0);
    return binDef;
  }

  // Helper to create a Piece with specific constraints for tests
  private Piece createPiece(
      String id,
      double w,
      double h,
      double d,
      double weight,
      int priority,
      int minQty,
      int maxQty) {
    // Using default values for value, rotation, stacking
    return new Piece(id, w, h, d, 0.0, weight, true, true, true, 99, priority, minQty, maxQty);
  }

  // Overload for simpler piece creation with defaults
  private Piece createPiece(String id, double w, double h, double d, double weight) {
    // Default priority 0, min 0, max Integer.MAX_VALUE
    return createPiece(id, w, h, d, weight, 0, 0, Integer.MAX_VALUE);
  }

  @Test
  void testPackSimpleFit() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>();
    pieces.add(createPiece("1", 5, 5, 5, 10.0)); // Use helper and String ID
    pieces.add(createPiece("2", 3, 3, 3, 5.0)); // Use helper and String ID

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getUnplacedPieces().isEmpty(),
        "Packing should succeed with no unplaced pieces"); // Check success via list
    assertEquals(
        2, result.getPlacedPieces().size(), "Should have placed 2 pieces"); // Use getPlacedPieces

    // Check positions
    Piece packedPiece1 =
        result.getPlacedPieces().stream() // Use getPlacedPieces
            .filter(p -> p.getId().equals("1")) // Use String ID
            .findFirst()
            .orElse(null);
    Piece packedPiece2 =
        result.getPlacedPieces().stream() // Use getPlacedPieces
            .filter(p -> p.getId().equals("2")) // Use String ID
            .findFirst()
            .orElse(null);

    assertNotNull(packedPiece1);
    assertNotNull(packedPiece1.getPosition());
    assertEquals(0, packedPiece1.getPosition().getX());
    assertEquals(0, packedPiece1.getPosition().getY());
    assertEquals(0, packedPiece1.getPosition().getZ());

    assertNotNull(packedPiece2);
    assertNotNull(packedPiece2.getPosition());
    // Position depends on algorithm, just check it exists
  }

  @Test
  void testPackPieceTooLarge() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>();
    pieces.add(createPiece("1", 15, 5, 5, 10.0)); // Too wide

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getPlacedPieces().isEmpty(),
        "Packing should fail (no placed pieces) if a piece is too large"); // Check failure via list
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Should have 1 unplaced piece"); // Use getUnplacedPieces
    assertEquals(
        "1", result.getUnplacedPieces().get(0).getId()); // Check ID of unplaced piece (String)
  }

  @Test
  void testPackPieceTooHeavy() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(50.0); // Max weight 50
    List<Piece> pieces = new ArrayList<>();
    pieces.add(createPiece("1", 5, 5, 5, 60.0)); // Weighs 60

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getPlacedPieces().isEmpty(),
        "Packing should fail (no placed pieces) if a piece is too heavy"); // Check failure via list
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Should have 1 unplaced piece"); // Use getUnplacedPieces
    assertEquals(
        "1", result.getUnplacedPieces().get(0).getId()); // Check ID of unplaced piece (String)
  }

  @Test
  void testPackMultiplePiecesOneDoesNotFit() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>();
    pieces.add(createPiece("1", 8, 8, 8, 10.0));
    pieces.add(createPiece("2", 5, 5, 5, 5.0)); // This one won't fit after Piece1

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    // Default algorithm packs what it can. Expect 1 placed, 1 unplaced.
    assertFalse(
        result.getPlacedPieces().isEmpty(),
        "Should have placed at least one piece"); // Check some success
    assertFalse(
        result.getUnplacedPieces().isEmpty(),
        "Should have at least one unplaced piece"); // Check some failure
    assertEquals(
        1, result.getPlacedPieces().size(), "Should have placed 1 piece"); // Use getPlacedPieces
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Should have 1 unplaced piece"); // Use getUnplacedPieces
    assertEquals("1", result.getPlacedPieces().get(0).getId()); // Check ID of placed piece (String)
    assertEquals(
        "2", result.getUnplacedPieces().get(0).getId()); // Check ID of unplaced piece (String)
  }

  @Test
  void testPackEmptyPieceList() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>(); // Empty list

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getPlacedPieces().isEmpty(),
        "Should have no placed pieces for empty input"); // Use getPlacedPieces
    assertTrue(
        result.getUnplacedPieces().isEmpty(),
        "Should have no unplaced pieces for empty input"); // Use getUnplacedPieces
  }

  // --- New Constraint Tests ---

  @Test
  void testPackTargetPercentageReached() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Volume = 1000
    BinDefinition binDef = createDefaultBinDef(1000.0);
    List<Piece> pieces = new ArrayList<>();
    // Use 5x5x5 pieces (Volume 125) that can fit together
    pieces.add(createPiece("1", 5, 5, 5, 10.0)); // 125
    pieces.add(createPiece("2", 5, 5, 5, 10.0)); // 250
    pieces.add(createPiece("3", 5, 5, 5, 10.0)); // 375
    pieces.add(createPiece("4", 5, 5, 5, 10.0)); // 500 (Target Met)
    pieces.add(createPiece("5", 5, 5, 5, 10.0)); // Should be unplaced

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = new PackingRequest.AlgorithmSettingsDto();
    settings.setTargetPercentage(40.0); // Target 40% = 400 volume

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);

    // Assert
    // Expect Pieces 1, 2, 3, 4 to be placed (500 volume >= 400 target)
    // Expect Piece 5 to be unplaced because target was met after Piece 4
    assertNotNull(result);
    assertEquals(4, result.getPlacedPieces().size(), "Should have placed 4 pieces to meet target");
    assertEquals(
        1, result.getUnplacedPieces().size(), "Should have 1 unplaced piece after target met");
    assertEquals("5", result.getUnplacedPieces().get(0).getId(), "Piece 5 should be unplaced");
    // Optional: Check for target message if the algorithm adds one
    // assertTrue(
    //    result.getStatusMessages().stream().anyMatch(m -> m.contains("Target volume
    // utilization")),
    //    "Should have status message about target");
  }

  @Test
  void testPackPriority() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>();
    // Piece 1: Lower priority (1), but fits easily first
    pieces.add(createPiece("1", 5, 5, 5, 10.0, 1, 0, Integer.MAX_VALUE));
    // Piece 2: Higher priority (0), slightly larger but should be packed first
    pieces.add(createPiece("2", 6, 6, 6, 15.0, 0, 0, Integer.MAX_VALUE));
    // Piece 3: Fits only if Piece 1 is packed first, not if Piece 2 is.
    pieces.add(createPiece("3", 5, 5, 5, 10.0, 2, 0, Integer.MAX_VALUE));

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();
    // No specific settings needed for default priority behavior

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    // Expect Piece 2 (priority 0) to be placed first due to sorting.
    // Piece 1 (priority 1) and Piece 3 (priority 2) will be attempted later.
    // Given the simple placement logic, after placing Piece 2 (6x6x6),
    // neither Piece 1 (5x5x5) nor Piece 3 (5x5x5) will likely fit.
    assertNotNull(result);
    assertEquals(1, result.getPlacedPieces().size(), "Should place Piece 2 (highest priority)");
    assertEquals("2", result.getPlacedPieces().get(0).getId(), "Placed piece should be Piece 2");
    assertEquals(2, result.getUnplacedPieces().size(), "Piece 1 and 3 should be unplaced");
    // Check that the specific unplaced pieces are 1 and 3
    assertTrue(
        result.getUnplacedPieces().stream().anyMatch(p -> p.getId().equals("1")),
        "Piece 1 should be in unplaced list");
    assertTrue(
        result.getUnplacedPieces().stream().anyMatch(p -> p.getId().equals("3")),
        "Piece 3 should be in unplaced list");
  }

  @Test
  void testPackMinQuantityNotMet() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Space for one 6x6x6, but not two
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>();
    // Piece A: minQty = 2, but only one fits. Priority 0.
    pieces.add(createPiece("A", 6, 6, 6, 10.0, 0, 2, 2)); // min 2, max 2
    pieces.add(createPiece("A", 6, 6, 6, 10.0, 0, 2, 2)); // Add the second instance
    // Piece B: Fits easily, lower priority 1.
    pieces.add(createPiece("B", 3, 3, 3, 5.0, 1, 0, Integer.MAX_VALUE));

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();
    // No specific settings needed

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    // Algorithm attempts to place 'A' first (priority 0). One 'A' fits.
    // Second 'A' fails.
    // Algorithm attempts to place 'B' (priority 1). 'B' fits.
    // Post-processing checks minQuantity for 'A'. Finds only 1 placed, needs 2.
    // Removes the placed 'A'.
    // Final state: Only 'B' is placed. Both 'A's are unplaced.
    assertNotNull(result);
    assertEquals(1, result.getPlacedPieces().size(), "Should place only piece B");
    assertEquals("B", result.getPlacedPieces().get(0).getId(), "Placed piece should be B");
    assertEquals(2, result.getUnplacedPieces().size(), "Both 'A' pieces should be unplaced");
    assertTrue(
        result.getUnplacedPieces().stream().allMatch(p -> p.getId().equals("A")),
        "Unplaced pieces should both be 'A'");
    assertTrue(
        result.getStatusMessages().stream().anyMatch(m -> m.contains("Minimum quantity")),
        "Should have status message about min quantity violation");
  }

  @Test
  void testPackMaxQuantityExceeded() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Enough space for both 'A's
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = new ArrayList<>();
    // Piece A: maxQty = 1. Provide two instances. Priority 0.
    pieces.add(createPiece("A", 6, 6, 6, 10.0, 0, 0, 1)); // min 0, max 1
    pieces.add(createPiece("A", 6, 6, 6, 10.0, 0, 0, 1)); // Add the second instance
    // Piece B: Fits easily. Priority 0 (same as A, volume smaller, so tried after A).
    pieces.add(createPiece("B", 3, 3, 3, 5.0, 0, 0, Integer.MAX_VALUE));

    PackingAlgorithm algorithm = new DefaultPackingAlgorithm();
    // No specific settings needed

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    // Pieces sorted by priority (all 0), then volume (A, A, B).
    // Algorithm attempts first 'A'. Fits.
    // Algorithm attempts second 'A'. Max quantity check (1 placed >= max 1) prevents placement.
    // Algorithm attempts 'B'. Fits.
    // Final state: One 'A' and one 'B' placed. One 'A' unplaced.
    assertNotNull(result);
    assertEquals(2, result.getPlacedPieces().size(), "Should place one 'A' and 'B'");
    assertTrue(
        result.getPlacedPieces().stream().anyMatch(p -> p.getId().equals("A")),
        "One 'A' should be placed");
    assertTrue(
        result.getPlacedPieces().stream().anyMatch(p -> p.getId().equals("B")),
        "'B' should be placed");
    assertEquals(1, result.getUnplacedPieces().size(), "One 'A' piece should be unplaced");
    assertEquals("A", result.getUnplacedPieces().get(0).getId(), "Unplaced piece should be 'A'");
    // Optional: Check status message if max quantity prevention adds one (currently it doesn't, it
    // just skips)
    // assertTrue(result.getStatusMessages().stream().anyMatch(m -> m.contains("maximum
    // quantity")),
    //            "Should have status message about max quantity");
  }
}
