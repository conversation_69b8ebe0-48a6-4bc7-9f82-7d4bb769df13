package com.packprime.innovademo;

import static org.junit.jupiter.api.Assertions.*;

import com.packprime.innovademo.algorithm.Bin;
import com.packprime.innovademo.algorithm.FirstFitDecreasingVolumeAlgorithm;
import com.packprime.innovademo.algorithm.PackingAlgorithm;
import com.packprime.innovademo.algorithm.PackingResult;
import com.packprime.innovademo.algorithm.Piece;
import com.packprime.innovademo.algorithm.VerticalOrientationConstraint;
import com.packprime.innovademo.dto.PackingRequest;
import com.packprime.innovademo.model.BinDefinition;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;

class FirstFitDecreasingVolumeAlgorithmTest {

  // Helper to create a default BinDefinition for tests
  private BinDefinition createDefaultBinDef(double maxWeight) {
    BinDefinition binDef = new BinDefinition();
    binDef.setMaxWeight(maxWeight);
    binDef.setOverhangX(0);
    binDef.setOverhangY(0);
    return binDef;
  }

  // Helper to create a Piece with default constraints for tests
  private Piece createPiece(String id, double w, double h, double d, double weight) {
    // Using default values for value, rotation, stacking, priority, quantity
    return new Piece(id, w, h, d, 0.0, weight, true, true, true, 99, 0, 1, 1);
  }

  @Test
  void testPackSimpleFitSorted() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Fix Bin constructor
    BinDefinition binDef = createDefaultBinDef(100.0); // Use helper
    // Pieces should be sorted by volume descending by the algorithm
    Piece largePiece = createPiece("1", 8, 8, 8, 20.0); // Volume 512, Use helper
    Piece smallPiece = createPiece("2", 3, 3, 3, 5.0); // Volume 27, Use helper
    List<Piece> pieces =
        new ArrayList<>(Arrays.asList(smallPiece, largePiece)); // Provide in unsorted order

    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null); // Fix pack call

    // Assert
    assertNotNull(result);
    // The small piece cannot fit after the large one in a 10x10x10 bin.
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Packing should leave 1 unplaced piece"); // Expect 1 unplaced
    assertEquals(
        1, result.getPlacedPieces().size(), "Should have placed 1 piece"); // Expect 1 placed
    assertEquals(
        "2",
        result.getUnplacedPieces().get(0).getId(),
        "Small piece (2) should be unplaced"); // Verify unplaced ID
    assertEquals(
        "1",
        result.getPlacedPieces().get(0).getId(),
        "Large piece (1) should be placed"); // Verify placed ID

    // Verify the large piece was packed (at origin)
    Piece packedLarge =
        result.getPlacedPieces().stream() // Use getPlacedPieces
            .filter(p -> p.getId().equals("1")) // Use String ID
            .findFirst()
            .orElse(null);
    Piece packedSmall =
        result.getPlacedPieces().stream() // Use getPlacedPieces
            .filter(p -> p.getId().equals("2")) // Use String ID
            .findFirst()
            .orElse(null);

    assertNotNull(packedLarge);
    assertNotNull(packedLarge.getPosition());
    assertEquals(0, packedLarge.getPosition().getX());
    assertEquals(0, packedLarge.getPosition().getY());
    assertEquals(0, packedLarge.getPosition().getZ());
  }

  @Test
  void testPackPieceTooLarge() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Fix Bin constructor
    BinDefinition binDef = createDefaultBinDef(100.0); // Use helper
    List<Piece> pieces = new ArrayList<>();
    pieces.add(createPiece("1", 15, 5, 5, 10.0)); // Too wide, Use helper

    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null); // Fix pack call

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getPlacedPieces().isEmpty(),
        "Packing should fail (no placed pieces) if a piece is too large"); // Check failure via list
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Should have 1 unplaced piece"); // Use getUnplacedPieces
    assertEquals(
        "1", result.getUnplacedPieces().get(0).getId()); // Check ID of unplaced piece (String)
  }

  @Test
  void testPackPieceTooHeavy() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Fix Bin constructor
    BinDefinition binDef = createDefaultBinDef(50.0); // Max weight 50, Use helper
    List<Piece> pieces = new ArrayList<>();
    pieces.add(createPiece("1", 5, 5, 5, 60.0)); // Weighs 60, Use helper

    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null); // Fix pack call

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getPlacedPieces().isEmpty(),
        "Packing should fail (no placed pieces) if a piece is too heavy"); // Check failure via list
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Should have 1 unplaced piece"); // Use getUnplacedPieces
    assertEquals(
        "1", result.getUnplacedPieces().get(0).getId()); // Check ID of unplaced piece (String)
  }

  @Test
  void testPackMultiplePiecesOneDoesNotFitDueToOrder() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Fix Bin constructor
    BinDefinition binDef = createDefaultBinDef(100.0); // Use helper
    Piece largePiece = createPiece("1", 8, 8, 8, 10.0); // Volume 512, Use helper
    Piece mediumPiece = createPiece("2", 5, 5, 5, 5.0); // Volume 125, Use helper
    // Large piece fits, medium piece does not fit after large piece
    List<Piece> pieces = new ArrayList<>(Arrays.asList(mediumPiece, largePiece)); // Unsorted

    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null); // Fix pack call

    // Assert
    assertNotNull(result);
    // FFDV packs what it can (the largest first). Expect 1 placed, 1 unplaced.
    assertFalse(
        result.getPlacedPieces().isEmpty(),
        "Should have placed at least one piece"); // Check some success
    assertFalse(
        result.getUnplacedPieces().isEmpty(),
        "Should have at least one unplaced piece"); // Check some failure
    assertEquals(
        1,
        result.getPlacedPieces().size(),
        "Should have placed 1 piece (the largest)"); // Use getPlacedPieces
    assertEquals(
        1,
        result.getUnplacedPieces().size(),
        "Should have 1 unplaced piece (the medium)"); // Use getUnplacedPieces
    assertEquals(
        "1",
        result.getPlacedPieces().get(0).getId(),
        "Large piece should be packed"); // Check ID (String)
    assertEquals(
        "2",
        result.getUnplacedPieces().get(0).getId(),
        "Medium piece should be unpacked"); // Check ID (String)
  }

  @Test
  void testPackEmptyPieceList() {
    // Arrange
    Bin bin = new Bin(10, 10, 10); // Fix Bin constructor
    BinDefinition binDef = createDefaultBinDef(100.0); // Use helper
    List<Piece> pieces = Collections.emptyList(); // Empty list

    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null); // Fix pack call

    // Assert
    assertNotNull(result);
    assertTrue(
        result.getPlacedPieces().isEmpty(),
        "Should have no placed pieces for empty input"); // Use getPlacedPieces
    assertTrue(
        result.getUnplacedPieces().isEmpty(),
        "Should have no unplaced pieces for empty input"); // Use getUnplacedPieces
  }
  
  // Helper method to create algorithm settings with a specific vertical constraint
  private PackingRequest.AlgorithmSettingsDto createSettingsWithVerticalConstraint(
      VerticalOrientationConstraint constraint) {
    PackingRequest.AlgorithmSettingsDto settings = new PackingRequest.AlgorithmSettingsDto();
    settings.setVerticalConstraint(constraint);
    return settings;
  }
  
  // Helper method to check if a piece was placed with the expected orientation
  private void assertPieceOrientationEquals(int expectedOrientation, Piece piece) {
    assertTrue(piece.isPlaced(), "Piece should be placed");
    assertEquals(expectedOrientation, piece.getOrientation(), 
        "Piece should have orientation " + expectedOrientation);
  }
  
  @Test
  void testVerticalConstraint_HeightOnly() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece that can be placed in any orientation
    Piece piece = createPiece("test", 5, 5, 5, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.HEIGHT_ONLY);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In HEIGHT_ONLY constraint, only orientations 0 and 1 are allowed (height is vertical)
    assertTrue(placedPiece.getOrientation() == 0 || placedPiece.getOrientation() == 1,
        "Orientation should be 0 or 1 when HEIGHT_ONLY constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_WidthOnly() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece that can be placed in any orientation
    Piece piece = createPiece("test", 5, 5, 5, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.WIDTH_ONLY);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In WIDTH_ONLY constraint, only orientations 2 and 3 are allowed (width is vertical)
    assertTrue(placedPiece.getOrientation() == 2 || placedPiece.getOrientation() == 3,
        "Orientation should be 2 or 3 when WIDTH_ONLY constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_LengthOnly() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece that can be placed in any orientation
    Piece piece = createPiece("test", 5, 5, 5, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.LENGTH_ONLY);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In LENGTH_ONLY constraint, only orientations 4 and 5 are allowed (length is vertical)
    assertTrue(placedPiece.getOrientation() == 4 || placedPiece.getOrientation() == 5,
        "Orientation should be 4 or 5 when LENGTH_ONLY constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_HeightOrWidth() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece with different dimensions to test orientation preference
    Piece piece = createPiece("test", 8, 4, 3, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.HEIGHT_OR_WIDTH);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In HEIGHT_OR_WIDTH constraint, orientations 0, 1, 2, 3 are allowed
    int orientation = placedPiece.getOrientation();
    assertTrue(orientation >= 0 && orientation <= 3,
        "Orientation should be 0, 1, 2, or 3 when HEIGHT_OR_WIDTH constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_HeightOrLength() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece with different dimensions to test orientation preference
    Piece piece = createPiece("test", 8, 4, 3, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.HEIGHT_OR_LENGTH);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In HEIGHT_OR_LENGTH constraint, orientations 0, 1, 4, 5 are allowed
    int orientation = placedPiece.getOrientation();
    assertTrue(orientation == 0 || orientation == 1 || orientation == 4 || orientation == 5,
        "Orientation should be 0, 1, 4, or 5 when HEIGHT_OR_LENGTH constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_WidthOrLength() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece with different dimensions to test orientation preference
    Piece piece = createPiece("test", 8, 4, 3, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.WIDTH_OR_LENGTH);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In WIDTH_OR_LENGTH constraint, orientations 2, 3, 4, 5 are allowed
    int orientation = placedPiece.getOrientation();
    assertTrue(orientation >= 2 && orientation <= 5,
        "Orientation should be 2, 3, 4, or 5 when WIDTH_OR_LENGTH constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_Any() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece with different dimensions to test orientation preference
    Piece piece = createPiece("test", 8, 4, 3, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    PackingRequest.AlgorithmSettingsDto settings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.ANY);
    
    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, settings);
    
    // Assert
    assertEquals(1, result.getPlacedPieces().size(), "One piece should be placed");
    Piece placedPiece = result.getPlacedPieces().get(0);
    
    // In ANY constraint, all orientations 0-5 are allowed
    int orientation = placedPiece.getOrientation();
    assertTrue(orientation >= 0 && orientation <= 5,
        "Orientation should be between 0 and 5 when ANY constraint is applied");
  }
  
  @Test
  void testVerticalConstraint_NonFittingOrientations() {
    // Arrange
    Bin bin = new Bin(10, 5, 10); // Height limited to 5
    BinDefinition binDef = createDefaultBinDef(100.0);
    
    // Create a piece that only fits in certain orientations
    // Important: We need to restrict rotations to make this test deterministic
    // allowRotationX=false, allowRotationY=false, allowRotationZ=false means only orientation 0 is allowed
    Piece piece = new Piece("test", 3, 8, 3, 0.0, 10.0, false, false, false, 99, 0, 1, 1);
    List<Piece> pieces = Collections.singletonList(piece);
    
    PackingAlgorithm algorithm = new FirstFitDecreasingVolumeAlgorithm();
    
    // Test with HEIGHT_ONLY constraint (should not fit because height=8 > bin height=5)
    PackingRequest.AlgorithmSettingsDto heightOnlySettings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.HEIGHT_ONLY);
    
    // Act
    PackingResult heightOnlyResult = algorithm.pack(bin, pieces, binDef, heightOnlySettings);
    
    // Assert
    assertEquals(0, heightOnlyResult.getPlacedPieces().size(), 
        "No pieces should be placed with HEIGHT_ONLY constraint when height exceeds bin height");
    assertEquals(1, heightOnlyResult.getUnplacedPieces().size(),
        "One piece should be unplaced with HEIGHT_ONLY constraint");
    
    // Now create a piece that allows rotations
    Piece rotatablePiece = createPiece("test2", 3, 8, 3, 10.0);
    List<Piece> rotatablePieces = Collections.singletonList(rotatablePiece);
    
    // Test with WIDTH_ONLY constraint (should fit because width=3 < bin height=5)
    PackingRequest.AlgorithmSettingsDto widthOnlySettings = 
        createSettingsWithVerticalConstraint(VerticalOrientationConstraint.WIDTH_ONLY);
    
    // Act
    PackingResult widthOnlyResult = algorithm.pack(bin, rotatablePieces, binDef, widthOnlySettings);
    
    // Assert
    assertEquals(1, widthOnlyResult.getPlacedPieces().size(), 
        "One piece should be placed with WIDTH_ONLY constraint");
    assertEquals(0, widthOnlyResult.getUnplacedPieces().size(),
        "No pieces should be unplaced with WIDTH_ONLY constraint");
    
    // Verify the orientation - should be 2 or 3 (width is vertical)
    Piece placedPiece = widthOnlyResult.getPlacedPieces().get(0);
    assertTrue(placedPiece.getOrientation() == 2 || placedPiece.getOrientation() == 3,
        "Piece should be placed with orientation 2 or 3 (width vertical)");
  }
}
