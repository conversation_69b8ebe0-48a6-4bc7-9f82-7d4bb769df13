{"name": "FFDV Vertical Constraint Test", "description": "Test case for the FirstFitDecreasingVolumeAlgorithm with vertical orientation constraints", "bin": {"width": 100, "height": 100, "depth": 100, "maxWeight": 1000, "overhangX": 0, "overhangY": 0}, "boxTypes": [{"id": "tall_box", "width": 30, "height": 80, "depth": 30, "weight": 150, "quantity": 2, "allowedVerticalOrientations": {"width": false, "height": true, "depth": false}, "shapeType": "Cuboid"}, {"id": "wide_box", "width": 70, "height": 40, "depth": 30, "weight": 180, "quantity": 1, "allowedVerticalOrientations": {"width": true, "height": false, "depth": false}, "shapeType": "Cuboid"}, {"id": "cube_box", "width": 40, "height": 40, "depth": 40, "weight": 100, "quantity": 1, "allowedVerticalOrientations": {"width": true, "height": true, "depth": true}, "shapeType": "Cuboid"}], "algorithmType": "FirstFitDecreasingVolume", "algorithmSettings": {"verticalConstraint": "HEIGHT_ONLY"}}