package com.packprime.innovademo.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.packprime.innovademo.model.RuleTemplate;
import com.packprime.innovademo.service.RuleService;
import com.packprime.innovademo.service.RuleValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for managing rule templates and user rules.
 */
@RestController
@RequestMapping("/api/rules")
public class RuleController {
    private static final Logger log = LoggerFactory.getLogger(RuleController.class);
    private final RuleService ruleService;
    private final RuleValidationService validationService;
    private final ObjectMapper objectMapper;
    
    public RuleController(RuleService ruleService, RuleValidationService validationService, ObjectMapper objectMapper) {
        this.ruleService = ruleService;
        this.validationService = validationService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * Gets a list of all built-in rule templates.
     * 
     * @return List of rule templates
     */
    @GetMapping("/templates")
    public ResponseEntity<List<RuleTemplate>> getBuiltInTemplates() {
        log.info("Received request for /api/rules/templates");
        try {
            List<RuleTemplate> templates = ruleService.getBuiltInTemplates();
            return ResponseEntity.ok(templates);
        } catch (Exception e) {
            log.error("Error getting built-in templates: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * Gets a list of all user-created rule templates.
     * 
     * @return List of user rule templates
     */
    @GetMapping("/user")
    public ResponseEntity<List<RuleTemplate>> getUserRules() {
        log.info("Received request for /api/rules/user");
        try {
            List<RuleTemplate> userRules = ruleService.getUserRules();
            return ResponseEntity.ok(userRules);
        } catch (Exception e) {
            log.error("Error getting user rules: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * Gets the content of a built-in template by ID.
     * 
     * @param id Template ID
     * @return Template content as JSON
     */
    @GetMapping("/template/{id}")
    public ResponseEntity<JsonNode> getBuiltInTemplateContent(@PathVariable String id) {
        log.info("Received request for /api/rules/template/{}", id);
        try {
            JsonNode content = ruleService.getBuiltInTemplateContent(id);
            return ResponseEntity.ok(content);
        } catch (IOException e) {
            log.error("Error getting built-in template content: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            log.error("Unexpected error getting built-in template content: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * Gets the content of a user rule by ID.
     * 
     * @param id Rule ID
     * @return Rule content as JSON
     */
    @GetMapping("/user/{id}")
    public ResponseEntity<JsonNode> getUserRuleContent(@PathVariable String id) {
        log.info("Received request for /api/rules/user/{}", id);
        try {
            JsonNode content = ruleService.getUserRuleContent(id);
            return ResponseEntity.ok(content);
        } catch (IOException e) {
            log.error("Error getting user rule content: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            log.error("Unexpected error getting user rule content: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * Saves a user rule.
     * 
     * @param requestBody Request body containing name and content
     * @return Saved rule template
     */
    @PostMapping("/user")
    public ResponseEntity<?> saveUserRule(@RequestBody Map<String, Object> requestBody) {
        log.info("Received request to save user rule");
        try {
            String name = (String) requestBody.get("name");
            if (name == null || name.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "Name is required"));
            }
            
            Object contentObj = requestBody.get("content");
            if (contentObj == null) {
                return ResponseEntity.badRequest().body(Map.of("error", "Content is required"));
            }
            
            JsonNode content = objectMapper.valueToTree(contentObj);
            
            // Validate the rule content
            List<String> validationErrors = validationService.validateRule(content);
            if (!validationErrors.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("valid", false);
                response.put("errors", validationErrors);
                return ResponseEntity.badRequest().body(response);
            }
            
            RuleTemplate savedRule = ruleService.saveUserRule(name, content);
            return ResponseEntity.status(HttpStatus.CREATED).body(savedRule);
        } catch (IOException e) {
            log.error("Error saving user rule: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error saving user rule: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * Deletes a user rule by ID.
     * 
     * @param id Rule ID
     * @return Success status
     */
    @DeleteMapping("/user/{id}")
    public ResponseEntity<Map<String, Object>> deleteUserRule(@PathVariable String id) {
        log.info("Received request to delete user rule {}", id);
        try {
            boolean deleted = ruleService.deleteUserRule(id);
            return ResponseEntity.ok(Map.of("success", deleted));
        } catch (Exception e) {
            log.error("Error deleting user rule: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * Validates a rule without saving it.
     * 
     * @param content Rule content to validate
     * @return Validation result
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateRule(@RequestBody JsonNode content) {
        log.info("Received request to validate rule");
        try {
            List<String> validationErrors = validationService.validateRule(content);
            Map<String, Object> response = new HashMap<>();
            response.put("valid", validationErrors.isEmpty());
            if (!validationErrors.isEmpty()) {
                response.put("errors", validationErrors);
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error validating rule: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("valid", false, "error", e.getMessage()));
        }
    }
}
