package com.packprime.innovademo.controller;

import com.packprime.innovademo.model.BinDefinition; // Import model
import com.packprime.innovademo.model.PieceDefinition; // Import model
import com.packprime.innovademo.repository.BinDefinitionRepository;
import com.packprime.innovademo.repository.PieceDefinitionRepository;
import java.util.List; // Import List
import java.util.Optional; // Import Optional
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity; // Import ResponseEntity
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/definitions")
public class DefinitionController {

  private final BinDefinitionRepository binDefinitionRepository;
  private final PieceDefinitionRepository pieceDefinitionRepository;

  @Autowired
  public DefinitionController(
      BinDefinitionRepository binDefinitionRepository,
      PieceDefinitionRepository pieceDefinitionRepository) {
    this.binDefinitionRepository = binDefinitionRepository;
    this.pieceDefinitionRepository = pieceDefinitionRepository;
  }

  // --- Bin Definition Endpoints ---

  @GetMapping("/bins")
  public ResponseEntity<List<BinDefinition>> getAllBinDefinitions() {
    List<BinDefinition> bins = binDefinitionRepository.findAll();
    return ResponseEntity.ok(bins);
  }

  @PostMapping("/bins")
  public ResponseEntity<BinDefinition> createBinDefinition(
      @RequestBody BinDefinition binDefinition) {
    // Ensure ID is null so it's treated as a new entity
    binDefinition.setId(null);
    BinDefinition savedBin = binDefinitionRepository.save(binDefinition);
    return ResponseEntity.ok(savedBin);
  }

  @GetMapping("/bins/{id}")
  public ResponseEntity<BinDefinition> getBinDefinitionById(@PathVariable Long id) {
    Optional<BinDefinition> binOpt = binDefinitionRepository.findById(id);
    return binOpt.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
  }

  @PutMapping("/bins/{id}")
  public ResponseEntity<BinDefinition> updateBinDefinition(
      @PathVariable Long id, @RequestBody BinDefinition updatedBin) {
    return binDefinitionRepository
        .findById(id)
        .map(
            existingBin -> {
              updatedBin.setId(id); // Ensure the ID is set for update
              BinDefinition savedBin = binDefinitionRepository.save(updatedBin);
              return ResponseEntity.ok(savedBin);
            })
        .orElseGet(() -> ResponseEntity.notFound().build());
  }

  @DeleteMapping("/bins/{id}")
  public ResponseEntity<Void> deleteBinDefinition(@PathVariable Long id) {
    if (!binDefinitionRepository.existsById(id)) {
      return ResponseEntity.notFound().build();
    }
    binDefinitionRepository.deleteById(id);
    return ResponseEntity.noContent().build();
  }

  // --- Piece Definition Endpoints ---

  @GetMapping("/pieces")
  public ResponseEntity<List<PieceDefinition>> getAllPieceDefinitions() {
    List<PieceDefinition> pieces = pieceDefinitionRepository.findAll();
    return ResponseEntity.ok(pieces);
  }

  @PostMapping("/pieces")
  public ResponseEntity<PieceDefinition> createPieceDefinition(
      @RequestBody PieceDefinition pieceDefinition) {
    // Ensure ID is null so it's treated as a new entity
    pieceDefinition.setId(null);
    PieceDefinition savedPiece = pieceDefinitionRepository.save(pieceDefinition);
    return ResponseEntity.ok(savedPiece);
  }

  @GetMapping("/pieces/{id}")
  public ResponseEntity<PieceDefinition> getPieceDefinitionById(@PathVariable Long id) {
    Optional<PieceDefinition> pieceOpt = pieceDefinitionRepository.findById(id);
    return pieceOpt.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
  }

  @PutMapping("/pieces/{id}")
  public ResponseEntity<PieceDefinition> updatePieceDefinition(
      @PathVariable Long id, @RequestBody PieceDefinition updatedPiece) {
    return pieceDefinitionRepository
        .findById(id)
        .map(
            existingPiece -> {
              updatedPiece.setId(id); // Ensure the ID is set for update
              PieceDefinition savedPiece = pieceDefinitionRepository.save(updatedPiece);
              return ResponseEntity.ok(savedPiece);
            })
        .orElseGet(() -> ResponseEntity.notFound().build());
  }

  @DeleteMapping("/pieces/{id}")
  public ResponseEntity<Void> deletePieceDefinition(@PathVariable Long id) {
    if (!pieceDefinitionRepository.existsById(id)) {
      return ResponseEntity.notFound().build();
    }
    pieceDefinitionRepository.deleteById(id);
    return ResponseEntity.noContent().build();
  }
}
