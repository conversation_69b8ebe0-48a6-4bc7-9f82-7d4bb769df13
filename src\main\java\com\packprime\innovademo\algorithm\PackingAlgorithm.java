package com.packprime.innovademo.algorithm;

import com.packprime.innovademo.dto.PackingRequest; // Import for AlgorithmSettingsDto
import com.packprime.innovademo.model.BinDefinition; // Import for Bin constraints
import java.util.List;

/** Interface for different bin packing algorithms. */
public interface PackingAlgorithm {

  /**
   * Attempts to pack the given pieces into the provided bin, considering various constraints.
   *
   * @param bin The bin instance to pack into. Its state (placed pieces, corners, weight) might be
   *     modified by the algorithm.
   * @param pieces The list of pieces available to pack. The algorithm should handle piece
   *     constraints (rotation, stacking, etc.).
   * @param binDefinition The definition of the bin, containing constraints like maxWeight,
   *     overhang.
   * @param settings Additional algorithm-specific settings or general constraints. Can be null.
   * @return A PackingResult object containing the list of placed pieces, unplaced pieces, and any
   *     status messages.
   */
  PackingResult pack(
      Bin bin,
      List<Piece> pieces,
      BinDefinition binDefinition,
      PackingRequest.AlgorithmSettingsDto settings);
}
