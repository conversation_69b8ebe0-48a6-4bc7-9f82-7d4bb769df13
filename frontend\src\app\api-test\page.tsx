"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import { ruleEditorService } from '@/types/rule-editor-service'
import { RuleTemplate, PackingRule } from '@/types/rule-types'
import { useRouter } from 'next/navigation'

export default function ApiTestPage() {
  const [builtInTemplates, setBuiltInTemplates] = useState<RuleTemplate[]>([])
  const [userRules, setUserRules] = useState<RuleTemplate[]>([])
  const [selectedRule, setSelectedRule] = useState<PackingRule | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<{[key: string]: {success: boolean, message: string}}>({})
  const router = useRouter()

  // Test fetching built-in templates
  const testFetchBuiltInTemplates = async () => {
    try {
      setLoading(true)
      setError(null)
      const templates = await ruleEditorService.getBuiltInTemplates()
      setBuiltInTemplates(templates)
      setTestResults(prev => ({
        ...prev,
        fetchBuiltInTemplates: {
          success: true,
          message: `Successfully fetched ${templates.length} built-in templates`
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setTestResults(prev => ({
        ...prev,
        fetchBuiltInTemplates: {
          success: false,
          message: `Error: ${errorMessage}`
        }
      }))
      setError(`Failed to fetch built-in templates: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // Test fetching user rules
  const testFetchUserRules = async () => {
    try {
      setLoading(true)
      setError(null)
      const rules = await ruleEditorService.getUserRules()
      setUserRules(rules)
      setTestResults(prev => ({
        ...prev,
        fetchUserRules: {
          success: true,
          message: `Successfully fetched ${rules.length} user rules`
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setTestResults(prev => ({
        ...prev,
        fetchUserRules: {
          success: false,
          message: `Error: ${errorMessage}`
        }
      }))
      setError(`Failed to fetch user rules: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // Test fetching a rule content
  const testFetchRuleContent = async (id: string, isBuiltIn: boolean) => {
    try {
      setLoading(true)
      setError(null)
      let rule
      if (isBuiltIn) {
        rule = await ruleEditorService.getBuiltInTemplateContent(id)
      } else {
        rule = await ruleEditorService.getUserRuleContent(id)
      }
      setSelectedRule(rule)
      setTestResults(prev => ({
        ...prev,
        fetchRuleContent: {
          success: true,
          message: `Successfully fetched rule content for ${id}`
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setTestResults(prev => ({
        ...prev,
        fetchRuleContent: {
          success: false,
          message: `Error: ${errorMessage}`
        }
      }))
      setError(`Failed to fetch rule content: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // Test validating a rule
  const testValidateRule = async () => {
    if (!selectedRule) {
      setError('No rule selected to validate')
      return
    }

    try {
      setLoading(true)
      setError(null)
      const result = await ruleEditorService.validateRule(selectedRule)
      setTestResults(prev => ({
        ...prev,
        validateRule: {
          success: result.valid,
          message: result.valid 
            ? 'Rule is valid' 
            : `Rule validation failed: ${result.errors?.join(', ')}`
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setTestResults(prev => ({
        ...prev,
        validateRule: {
          success: false,
          message: `Error: ${errorMessage}`
        }
      }))
      setError(`Failed to validate rule: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // Run all tests
  const runAllTests = async () => {
    await testFetchBuiltInTemplates()
    await testFetchUserRules()
    if (builtInTemplates.length > 0) {
      await testFetchRuleContent(builtInTemplates[0].id, true)
      if (selectedRule) {
        await testValidateRule()
      }
    } else if (userRules.length > 0) {
      await testFetchRuleContent(userRules[0].id, false)
      if (selectedRule) {
        await testValidateRule()
      }
    }
  }

  // Render test result
  const renderTestResult = (key: string) => {
    const result = testResults[key]
    if (!result) return null

    return (
      <Alert variant={result.success ? 'default' : 'destructive'} className="mt-2">
        {result.success ? (
          <CheckCircle2 className="h-4 w-4" />
        ) : (
          <AlertCircle className="h-4 w-4" />
        )}
        <AlertTitle>{key}</AlertTitle>
        <AlertDescription>{result.message}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Rule Editor API Test</h1>
        <Button variant="outline" onClick={() => router.push('/')}>
          Back to Home
        </Button>
      </div>
      
      <Separator className="my-4" />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>API Tests</CardTitle>
              <CardDescription>Test the rule editor API endpoints</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={testFetchBuiltInTemplates} disabled={loading} className="w-full">
                Test Fetch Built-in Templates
              </Button>
              <Button onClick={testFetchUserRules} disabled={loading} className="w-full">
                Test Fetch User Rules
              </Button>
              <Button 
                onClick={() => builtInTemplates.length > 0 && testFetchRuleContent(builtInTemplates[0].id, true)} 
                disabled={loading || builtInTemplates.length === 0} 
                className="w-full"
              >
                Test Fetch Built-in Rule Content
              </Button>
              <Button 
                onClick={() => userRules.length > 0 && testFetchRuleContent(userRules[0].id, false)} 
                disabled={loading || userRules.length === 0} 
                className="w-full"
              >
                Test Fetch User Rule Content
              </Button>
              <Button 
                onClick={testValidateRule} 
                disabled={loading || !selectedRule} 
                className="w-full"
              >
                Test Validate Rule
              </Button>
              <Button 
                onClick={runAllTests} 
                disabled={loading} 
                className="w-full"
                variant="default"
              >
                Run All Tests
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>Results of API tests</CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <Tabs defaultValue="results">
                <TabsList>
                  <TabsTrigger value="results">Test Results</TabsTrigger>
                  <TabsTrigger value="templates">Templates</TabsTrigger>
                  <TabsTrigger value="userRules">User Rules</TabsTrigger>
                  <TabsTrigger value="selectedRule">Selected Rule</TabsTrigger>
                </TabsList>
                
                <TabsContent value="results" className="space-y-4">
                  {Object.keys(testResults).length === 0 ? (
                    <p className="text-muted-foreground">No tests have been run yet.</p>
                  ) : (
                    <>
                      {renderTestResult('fetchBuiltInTemplates')}
                      {renderTestResult('fetchUserRules')}
                      {renderTestResult('fetchRuleContent')}
                      {renderTestResult('validateRule')}
                    </>
                  )}
                </TabsContent>
                
                <TabsContent value="templates">
                  {builtInTemplates.length === 0 ? (
                    <p className="text-muted-foreground">No built-in templates fetched yet.</p>
                  ) : (
                    <div className="space-y-2">
                      {builtInTemplates.map(template => (
                        <Card key={template.id} className="p-4">
                          <h3 className="font-semibold">{template.name}</h3>
                          <p className="text-sm text-muted-foreground">{template.description}</p>
                          <p className="text-xs mt-1">Algorithm: {template.algorithmType}</p>
                          <p className="text-xs">ID: {template.id}</p>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="userRules">
                  {userRules.length === 0 ? (
                    <p className="text-muted-foreground">No user rules fetched yet.</p>
                  ) : (
                    <div className="space-y-2">
                      {userRules.map(rule => (
                        <Card key={rule.id} className="p-4">
                          <h3 className="font-semibold">{rule.name}</h3>
                          <p className="text-sm text-muted-foreground">{rule.description}</p>
                          <p className="text-xs mt-1">Algorithm: {rule.algorithmType}</p>
                          <p className="text-xs">ID: {rule.id}</p>
                          {rule.createdAt && <p className="text-xs">Created: {rule.createdAt}</p>}
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="selectedRule">
                  {!selectedRule ? (
                    <p className="text-muted-foreground">No rule content fetched yet.</p>
                  ) : (
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-semibold">{selectedRule.name}</h3>
                        <p className="text-sm text-muted-foreground">{selectedRule.description}</p>
                        <p className="text-xs mt-1">Algorithm: {selectedRule.algorithmType}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm">Bin Configuration</h4>
                        <p className="text-xs">
                          Dimensions: {selectedRule.bin.width} × {selectedRule.bin.height} × {selectedRule.bin.depth}
                        </p>
                        {selectedRule.bin.maxWeight && (
                          <p className="text-xs">Max Weight: {selectedRule.bin.maxWeight}</p>
                        )}
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm">Box Types ({selectedRule.boxTypes.length})</h4>
                        <div className="space-y-2 mt-2">
                          {selectedRule.boxTypes.map((box, index) => (
                            <div key={index} className="text-xs border p-2 rounded">
                              <p>ID: {box.id}</p>
                              <p>Dimensions: {box.width} × {box.height} × {box.depth}</p>
                              <p>Weight: {box.weight}</p>
                              <p>Quantity: {box.quantity}</p>
                              {box.shapeType && <p>Shape: {box.shapeType}</p>}
                              {box.diameter && <p>Diameter: {box.diameter}</p>}
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm">Algorithm Settings</h4>
                        <pre className="text-xs bg-muted p-2 rounded">
                          {JSON.stringify(selectedRule.algorithmSettings || {}, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
