/**
 * Primary Packs Form Component
 * This component orchestrates the primary packs management by combining table and edit form
 */

import React, { useState } from 'react';
import { PieceDto } from '@/types';
import { cn, generateId } from '@/lib/utils';
import PrimaryPackEditForm from './primary-pack-edit-form';
import PrimaryPacksTable from './primary-pack-table';

// Props for the PrimaryPacksForm component
interface PrimaryPacksFormProps {
  value: Array<PieceDto & { name: string; quantity: number }>;
  onChange: (value: Array<PieceDto & { name: string; quantity: number }>) => void;
  className?: string;
}

/**
 * Form for managing primary pack details
 */
const PrimaryPacksForm: React.FC<PrimaryPacksFormProps> = ({
  value,
  onChange,
  className,
}) => {
  // State for the currently editing pack
  const [editingPack, setEditingPack] = useState<(PieceDto & { name: string; quantity: number }) | null>(null);
  const [isAdding, setIsAdding] = useState(false);

  // <PERSON>le adding a new pack
  const handleAddPack = () => {
    setEditingPack({
      id: generateId(),
      name: '',
      width: 0,
      height: 0,
      depth: 0,
      weight: 0,
      quantity: 1,
      allowRotationX: true,
      allowRotationY: true,
      allowRotationZ: true,
      shapeType: 'Cuboid',
    });
    setIsAdding(true);
  };

  // Handle editing an existing pack
  const handleEditPack = (pack: PieceDto & { name: string; quantity: number }) => {
    setEditingPack({ ...pack });
    setIsAdding(false);
  };

  // Handle deleting a pack
  const handleDeletePack = (id: string) => {
    onChange(value.filter(pack => pack.id !== id));
  };

  // Handle saving the editing pack
  const handleSavePack = () => {
    if (!editingPack) return;
    
    // For cylinders, set width and height based on diameter
    if (editingPack.shapeType === 'Cylinder' && editingPack.diameter) {
      editingPack.width = editingPack.diameter;
      editingPack.height = editingPack.diameter;
    }
    
    if (isAdding) {
      // Add new pack
      onChange([...value, editingPack]);
    } else {
      // Update existing pack
      onChange(value.map(pack => 
        pack.id === editingPack.id ? editingPack : pack
      ));
    }
    
    setEditingPack(null);
  };

  // Handle input changes for the editing pack
  const handleEditingInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (!editingPack) return;
    
    const { name, value: inputValue, type } = e.target as HTMLInputElement;
    let parsedValue: string | number | boolean = inputValue;
    
    // Parse numeric values
    if (name !== 'name' && name !== 'shapeType') {
      if (type === 'checkbox') {
        parsedValue = (e.target as HTMLInputElement).checked;
      } else if (type !== 'checkbox') {
        const numValue = parseFloat(inputValue);
        if (!isNaN(numValue) && numValue > 0) {
          parsedValue = numValue;
        } else {
          return; // Don't update if not a valid number
        }
      }
    }
    
    setEditingPack({
      ...editingPack,
      [name]: parsedValue,
    });
  };

  // Handle select changes (for shape type)
  const handleSelectChange = (name: string, value: string) => {
    if (!editingPack) return;
    
    setEditingPack({
      ...editingPack,
      [name]: value,
    });
  };

  return (
    <div className={cn("", className)}>
      {/* Table component */}
      <PrimaryPacksTable 
        packs={value}
        onAddPack={handleAddPack}
        onEditPack={handleEditPack}
        onDeletePack={handleDeletePack}
      />
      
      {/* Edit form component */}
      {editingPack && (
        <PrimaryPackEditForm
          editingPack={editingPack}
          isAdding={isAdding}
          onSave={handleSavePack}
          onCancel={() => setEditingPack(null)}
          onChange={handleEditingInputChange}
          onSelectChange={handleSelectChange}
        />
      )}
    </div>
  );
};

export default PrimaryPacksForm;
