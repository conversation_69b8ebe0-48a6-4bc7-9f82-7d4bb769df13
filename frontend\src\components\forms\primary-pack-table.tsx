/**
 * Primary Packs Table Component
 * This component displays primary packs in a table view with actions
 */

import React from 'react';
import { PieceDto } from '@/types';
import { cn } from '@/lib/utils';

// Props for the PrimaryPacksTable component
interface PrimaryPacksTableProps {
  packs: Array<PieceDto & { name: string; quantity: number }>;
  onAddPack: () => void;
  onEditPack: (pack: PieceDto & { name: string; quantity: number }) => void;
  onDeletePack: (id: string) => void;
  className?: string;
}

/**
 * Table for displaying primary packs with actions
 */
const PrimaryPacksTable: React.FC<PrimaryPacksTableProps> = ({
  packs,
  onAddPack,
  onEditPack,
  onDeletePack,
  className,
}) => {
  // Format dimensions based on shape type
  const formatDimensions = (pack: PieceDto & { name: string; quantity: number }) => {
    if (pack.shapeType === 'Cylinder' && pack.diameter) {
      return `Ø${pack.diameter} × ${pack.depth} (h)`;
    }
    return `${pack.width} × ${pack.height} × ${pack.depth}`;
  };

  return (
    <div className={cn("p-4 bg-white dark:bg-gray-800 rounded-lg shadow", className)}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-blue-600 dark:text-blue-400">Primary Packs</h2>
        <button
          onClick={onAddPack}
          className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          + Add Pack
        </button>
      </div>
      
      {/* Table of primary packs */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Shape</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Dimensions (mm)</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Weight (kg)</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Quantity</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
            {packs.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  No primary packs added. Click "Add Pack" to add one.
                </td>
              </tr>
            ) : (
              packs.map(pack => (
                <tr key={pack.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {pack.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {pack.shapeType || 'Cuboid'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {formatDimensions(pack)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {pack.weight}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {pack.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => onEditPack(pack)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => onDeletePack(pack.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PrimaryPacksTable;
