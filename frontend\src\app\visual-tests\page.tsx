"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, CheckCircle2, Play, FileJson } from 'lucide-react'
import PackingVisualization from '@/components/three/frontend-visualization'
import { BinDto, PackingResponse } from '@/types/frontend-types'
import { performPacking } from '@/types/frontend-api-service'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

interface VisualTest {
  id: string
  name: string
  description: string
  algorithmType: string
}

interface TestResult {
  testId: string
  status: 'idle' | 'loading' | 'success' | 'error'
  response?: PackingResponse
  error?: string
  bin?: BinDto
}

export default function VisualTestsPage() {
  const [tests, setTests] = useState<VisualTest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTest, setActiveTest] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const router = useRouter()

  // Fetch the list of available visual tests
  useEffect(() => {
    const fetchTests = async () => {
      try {
        const response = await fetch('http://localhost:8080/api/visual-tests')
        if (!response.ok) {
          throw new Error(`Failed to fetch tests: ${response.status} ${response.statusText}`)
        }
        const data = await response.json()
        setTests(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch visual tests')
      } finally {
        setLoading(false)
      }
    }

    fetchTests()
  }, [])

  // Run a specific test
  const runTest = async (testId: string) => {
    // Update test status to loading
    setTestResults(prev => {
      const existingIndex = prev.findIndex(r => r.testId === testId)
      if (existingIndex >= 0) {
        const updated = [...prev]
        updated[existingIndex] = { ...updated[existingIndex], status: 'loading' }
        return updated
      }
      return [...prev, { testId, status: 'loading' }]
    })

    try {
      // Fetch the test data
      const response = await fetch(`http://localhost:8080/api/industry-data/${testId}`)
      if (!response.ok) {
        throw new Error(`Failed to fetch test data: ${response.status} ${response.statusText}`)
      }
      
      const testData = await response.json()
      
      // Create packing request from test data
      const packingRequest = {
        bin: testData.bin,
        pieces: testData.boxTypes.flatMap((box: any) => {
          // Create multiple pieces based on quantity
          const pieces = []
          for (let i = 0; i < box.quantity; i++) {
            pieces.push({
              id: `${box.id}_instance_${i}`,
              width: box.width,
              height: box.height,
              depth: box.depth,
              weight: box.weight,
              allowRotationX: true,
              allowRotationY: true,
              allowRotationZ: true,
              shapeType: box.shapeType || 'Cuboid',
              ...(box.diameter && { diameter: box.diameter })
            })
          }
          return pieces
        }),
        algorithmType: testData.algorithmType || 'FirstFitDecreasingVolume',
        algorithmSettings: testData.algorithmSettings || {}
      }
      
      // Call the packing API
      const packingResponse = await performPacking(packingRequest)
      
      // Update test results
      setTestResults(prev => {
        const existingIndex = prev.findIndex(r => r.testId === testId)
        if (existingIndex >= 0) {
          const updated = [...prev]
          updated[existingIndex] = { 
            testId, 
            status: 'success', 
            response: packingResponse,
            bin: testData.bin
          }
          return updated
        }
        return [...prev, { 
          testId, 
          status: 'success', 
          response: packingResponse,
          bin: testData.bin
        }]
      })
      
      // Set this as the active test
      setActiveTest(testId)
      
    } catch (err) {
      // Update test results with error
      setTestResults(prev => {
        const existingIndex = prev.findIndex(r => r.testId === testId)
        if (existingIndex >= 0) {
          const updated = [...prev]
          updated[existingIndex] = { 
            testId, 
            status: 'error', 
            error: err instanceof Error ? err.message : 'Failed to run test'
          }
          return updated
        }
        return [...prev, { 
          testId, 
          status: 'error', 
          error: err instanceof Error ? err.message : 'Failed to run test'
        }]
      })
    }
  }

  // Get the result for a specific test
  const getTestResult = (testId: string): TestResult | undefined => {
    return testResults.find(r => r.testId === testId)
  }

  // Render test status indicator
  const renderTestStatus = (testId: string) => {
    const result = getTestResult(testId)
    
    if (!result) {
      return null
    }
    
    if (result.status === 'loading') {
      return <Skeleton className="h-4 w-16" />
    }
    
    if (result.status === 'error') {
      return (
        <div className="flex items-center text-destructive">
          <AlertCircle className="h-4 w-4 mr-1" />
          <span>Failed</span>
        </div>
      )
    }
    
    if (result.status === 'success') {
      return (
        <div className="flex items-center text-green-600">
          <CheckCircle2 className="h-4 w-4 mr-1" />
          <span>Success</span>
        </div>
      )
    }
    
    return null
  }

  // Get the active test result
  const activeTestResult = activeTest ? getTestResult(activeTest) : undefined

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Visual Algorithm Tests</h1>
        <Button variant="outline" onClick={() => router.push('/')}>
          Back to Home
        </Button>
      </div>
      
      <Separator className="my-4" />
      
      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      ) : error ? (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold mb-4">Available Tests</h2>
              {tests.map(test => (
                <Card key={test.id} className={activeTest === test.id ? 'border-primary' : ''}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{test.name}</CardTitle>
                      {renderTestStatus(test.id)}
                    </div>
                    <CardDescription>{test.description}</CardDescription>
                  </CardHeader>
                  <CardFooter className="pt-2">
                    <Button 
                      onClick={() => runTest(test.id)}
                      disabled={getTestResult(test.id)?.status === 'loading'}
                      className="w-full"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Run Test
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
          
          <div className="md:col-span-2">
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>
                  {activeTest 
                    ? `Visualization for ${tests.find(t => t.id === activeTest)?.name || activeTest}`
                    : 'Select a test to run and visualize the results'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!activeTest ? (
                  <div className="flex items-center justify-center h-[500px] bg-muted/20 rounded-md">
                    <p className="text-muted-foreground">No test selected</p>
                  </div>
                ) : activeTestResult?.status === 'loading' ? (
                  <div className="flex items-center justify-center h-[500px] bg-muted/20 rounded-md">
                    <div className="text-center">
                      <Skeleton className="h-8 w-32 mx-auto mb-4" />
                      <p className="text-muted-foreground">Running test...</p>
                    </div>
                  </div>
                ) : activeTestResult?.status === 'error' ? (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Test Failed</AlertTitle>
                    <AlertDescription>{activeTestResult.error}</AlertDescription>
                  </Alert>
                ) : activeTestResult?.status === 'success' && activeTestResult.response && activeTestResult.bin ? (
                  <div className="h-[500px]">
                    <PackingVisualization 
                      secondaryPack={activeTestResult.bin}
                      packingResult={activeTestResult.response}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[500px] bg-muted/20 rounded-md">
                    <p className="text-muted-foreground">No results available</p>
                  </div>
                )}
              </CardContent>
              {activeTestResult?.status === 'success' && activeTestResult.response && (
                <CardFooter className="flex-col items-start">
                  <Tabs defaultValue="summary" className="w-full">
                    <TabsList>
                      <TabsTrigger value="summary">Summary</TabsTrigger>
                      <TabsTrigger value="placed">Placed Items</TabsTrigger>
                      <TabsTrigger value="unplaced">Unplaced Items</TabsTrigger>
                    </TabsList>
                    <TabsContent value="summary" className="space-y-2 mt-2">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-sm font-medium">Algorithm Used</p>
                          <p className="text-sm">{activeTestResult.response.algorithmUsed}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Volume Utilization</p>
                          <p className="text-sm">{(activeTestResult.response.volumeUtilization).toFixed(2)}%</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Total Weight</p>
                          <p className="text-sm">{activeTestResult.response.totalWeight}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Placed/Total Items</p>
                          <p className="text-sm">
                            {activeTestResult.response.placedPieces.length}/{
                              activeTestResult.response.placedPieces.length + 
                              activeTestResult.response.unplacedPieceIds.length
                            }
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Bin Dimensions</p>
                          <p className="text-sm">
                            {activeTestResult.bin ? 
                              `${activeTestResult.bin.width} × ${activeTestResult.bin.height} × ${activeTestResult.bin.depth}` : 
                              'N/A'}
                          </p>
                        </div>
                      </div>
                      
                      <div className="mt-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" className="w-full">
                              <FileJson className="h-4 w-4 mr-2" />
                              View Request JSON
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Request Configuration</DialogTitle>
                              <DialogDescription>
                                The JSON configuration used for this packing request.
                              </DialogDescription>
                            </DialogHeader>
                            <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg overflow-auto max-h-96 text-xs">
                              {JSON.stringify({
                                bin: activeTestResult.bin || {},
                                pieces: activeTestResult.response.pieces || [],
                                algorithm: activeTestResult.response.algorithmUsed
                              }, null, 2)}
                            </pre>
                          </DialogContent>
                        </Dialog>
                      </div>
                      
                      {activeTestResult.response.statusMessages && activeTestResult.response.statusMessages.length > 0 && (
                        <div>
                          <p className="text-sm font-medium">Status Messages</p>
                          <ul className="text-sm list-disc pl-5">
                            {activeTestResult.response.statusMessages.map((msg, i) => (
                              <li key={i}>{msg}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </TabsContent>
                    <TabsContent value="placed" className="mt-2">
                      <div className="max-h-48 overflow-y-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-2">ID</th>
                              <th className="text-left py-2">Dimensions</th>
                              <th className="text-left py-2">Position</th>
                              <th className="text-left py-2">Orientation</th>
                            </tr>
                          </thead>
                          <tbody>
                            {activeTestResult.response.placedPieces.map((piece, i) => (
                              <tr key={i} className="border-b">
                                <td className="py-2">{piece.id}</td>
                                <td className="py-2">{piece.placedWidth}x{piece.placedHeight}x{piece.placedDepth}</td>
                                <td className="py-2">({piece.position.x}, {piece.position.y}, {piece.position.z})</td>
                                <td className="py-2">{piece.orientation}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </TabsContent>
                    <TabsContent value="unplaced" className="mt-2">
                      {activeTestResult.response.unplacedPieceIds.length === 0 ? (
                        <p className="text-sm">All items were successfully placed.</p>
                      ) : (
                        <div className="max-h-48 overflow-y-auto">
                          <ul className="text-sm list-disc pl-5">
                            {activeTestResult.response.unplacedPieceIds.map((id, i) => (
                              <li key={i}>{id}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardFooter>
              )}
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}
