package com.packprime.innovademo.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "piece_definitions")
public class PieceDefinition {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id; // Database ID

  private String externalId; // Optional ID from input JSON
  private double width;
  private double height;
  private double depth;

  @Column(name = "\"value\"") // Quote column name as 'value' is a reserved keyword in H2
  private double value; // Optional value from input JSON

  private double weight; // Added weight constraint
  private boolean allowRotationX = true; // Added orientation constraint
  private boolean allowRotationY = true; // Added orientation constraint
  private boolean allowRotationZ = true; // Added orientation constraint
  private int stackingLimit = Integer.MAX_VALUE; // Added stacking constraint
  private int loadPriority = 0; // Added priority constraint
  private int minQuantity = 0; // Added quantity constraint
  private int maxQuantity = Integer.MAX_VALUE; // Added quantity constraint

  // Constructors
  public PieceDefinition() {}

  // Constructor with all fields including new constraints
  public PieceDefinition(
      String externalId,
      double width,
      double height,
      double depth,
      double value,
      double weight,
      boolean allowRotationX,
      boolean allowRotationY,
      boolean allowRotationZ,
      int stackingLimit,
      int loadPriority,
      int minQuantity,
      int maxQuantity) {
    this.externalId = externalId;
    this.width = width;
    this.height = height;
    this.depth = depth;
    this.value = value;
    this.weight = weight;
    this.allowRotationX = allowRotationX;
    this.allowRotationY = allowRotationY;
    this.allowRotationZ = allowRotationZ;
    this.stackingLimit = stackingLimit;
    this.loadPriority = loadPriority;
    this.minQuantity = minQuantity;
    this.maxQuantity = maxQuantity;
  }

  // Getters and Setters
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getExternalId() {
    return externalId;
  }

  public void setExternalId(String externalId) {
    this.externalId = externalId;
  }

  public double getWidth() {
    return width;
  }

  public void setWidth(double width) {
    this.width = width;
  }

  public double getHeight() {
    return height;
  }

  public void setHeight(double height) {
    this.height = height;
  }

  public double getDepth() {
    return depth;
  }

  public void setDepth(double depth) {
    this.depth = depth;
  }

  public double getValue() {
    return value;
  }

  public void setValue(double value) {
    this.value = value;
  }

  public double getWeight() {
    return weight;
  }

  public void setWeight(double weight) {
    this.weight = weight;
  }

  public boolean isAllowRotationX() {
    return allowRotationX;
  }

  public void setAllowRotationX(boolean allowRotationX) {
    this.allowRotationX = allowRotationX;
  }

  public boolean isAllowRotationY() {
    return allowRotationY;
  }

  public void setAllowRotationY(boolean allowRotationY) {
    this.allowRotationY = allowRotationY;
  }

  public boolean isAllowRotationZ() {
    return allowRotationZ;
  }

  public void setAllowRotationZ(boolean allowRotationZ) {
    this.allowRotationZ = allowRotationZ;
  }

  public int getStackingLimit() {
    return stackingLimit;
  }

  public void setStackingLimit(int stackingLimit) {
    this.stackingLimit = stackingLimit;
  }

  public int getLoadPriority() {
    return loadPriority;
  }

  public void setLoadPriority(int loadPriority) {
    this.loadPriority = loadPriority;
  }

  public int getMinQuantity() {
    return minQuantity;
  }

  public void setMinQuantity(int minQuantity) {
    this.minQuantity = minQuantity;
  }

  public int getMaxQuantity() {
    return maxQuantity;
  }

  public void setMaxQuantity(int maxQuantity) {
    this.maxQuantity = maxQuantity;
  }

  // toString (optional, for debugging)
  @Override
  public String toString() {
    return "PieceDefinition{"
        + "id="
        + id
        + ", externalId='"
        + externalId
        + '\''
        + ", width="
        + width
        + ", height="
        + height
        + ", depth="
        + depth
        + ", value="
        + value
        + ", weight="
        + weight
        + ", allowRotationX="
        + allowRotationX
        + ", allowRotationY="
        + allowRotationY
        + ", allowRotationZ="
        + allowRotationZ
        + ", stackingLimit="
        + stackingLimit
        + ", loadPriority="
        + loadPriority
        + ", minQuantity="
        + minQuantity
        + ", maxQuantity="
        + maxQuantity
        + '}';
  }
}
