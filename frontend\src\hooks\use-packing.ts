/**
 * Custom hook for managing packing logic
 */

import { useState, useCallback } from 'react';
import { 
  PackingFormState, 
  PackingResponse, 
  PackingRequest,
  PackingConfiguration,
  IndustryPackingRequest
} from '../types/frontend-types';
import { performPacking, performIndustryPacking } from '../lib/api';
import { 
  saveConfiguration, 
  loadConfiguration, 
  getAllConfigurations,
  deleteConfiguration,
  exportConfigurationToFile,
  importConfigurationFromFile
} from '../lib/storage';

interface UsePackingOptions {
  initialFormState?: PackingFormState;
}

interface UsePackingResult {
  formState: PackingFormState;
  setFormState: (state: PackingFormState) => void;
  packingResult: PackingResponse | undefined;
  isLoading: boolean;
  error: string | undefined;
  handlePack: () => Promise<void>;
  handleIndustryPack: (industryData: any) => Promise<void>;
  handleSaveConfiguration: (name: string) => void;
  handleLoadConfiguration: (name: string) => void;
  handleDeleteConfiguration: (name: string) => void;
  handleExportConfiguration: (name: string) => void;
  handleImportConfiguration: (file: File) => Promise<void>;
  savedConfigurations: PackingConfiguration[];
  refreshSavedConfigurations: () => void;
  setSelectedIndustryData: (filename: string | undefined) => void;
}

const defaultFormState: PackingFormState = {
  secondaryPack: {
    width: 800,
    height: 600,
    depth: 400,
    maxWeight: 25,
  },
  primaryPacks: [],
  algorithmType: 'Default',
};

export function usePacking({ initialFormState = defaultFormState }: UsePackingOptions = {}): UsePackingResult {
  const [formState, setFormState] = useState<PackingFormState>(initialFormState);
  const [packingResult, setPackingResult] = useState<PackingResponse | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const [savedConfigurations, setSavedConfigurations] = useState<PackingConfiguration[]>(getAllConfigurations());

  // Handle packing
  const handlePack = async () => {
    try {
      setIsLoading(true);
      setError(undefined);

      // Convert form state to API request
      const request: PackingRequest = {
        bin: formState.secondaryPack,
        pieces: formState.primaryPacks.flatMap(pack => {
          // Create multiple pieces based on quantity
          return Array.from({ length: pack.quantity }).map((_, index) => ({
            id: `${pack.id}_instance_${index + 1}`,
            width: pack.width,
            height: pack.height,
            depth: pack.depth,
            weight: pack.weight,
            allowRotationX: pack.allowRotationX ?? true,
            allowRotationY: pack.allowRotationY ?? true,
            allowRotationZ: pack.allowRotationZ ?? true,
            shapeType: pack.shapeType || 'Cuboid',
            diameter: pack.diameter,
          }));
        }),
        algorithmType: formState.algorithmType,
        algorithmSettings: formState.algorithmSettings,
      };

      // Call API
      const result = await performPacking(request);
      setPackingResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error performing packing:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle industry packing
  const handleIndustryPack = async (industryData: any) => {
    try {
      setIsLoading(true);
      setError(undefined);

      // Convert form state and industry data to API request
      const request: IndustryPackingRequest = {
        bin: formState.secondaryPack,
        boxTypes: industryData.boxTypes || []
      };

      // Call API
      const result = await performIndustryPacking(request);
      setPackingResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error performing industry packing:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Set selected industry data
  const setSelectedIndustryData = useCallback((filename: string | undefined) => {
    setFormState(prev => ({
      ...prev,
      selectedIndustryData: filename
    }));
  }, []);

  // Refresh saved configurations
  const refreshSavedConfigurations = () => {
    setSavedConfigurations(getAllConfigurations());
  };

  // Handle save configuration
  const handleSaveConfiguration = (name: string) => {
    if (!name.trim()) {
      setError('Please enter a configuration name');
      return;
    }

    try {
      const config: PackingConfiguration = {
        name,
        timestamp: Date.now(),
        secondaryPack: formState.secondaryPack,
        primaryPacks: formState.primaryPacks,
        algorithmType: formState.algorithmType,
        algorithmSettings: formState.algorithmSettings,
      };

      saveConfiguration(config);
      refreshSavedConfigurations();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save configuration');
    }
  };

  // Handle load configuration
  const handleLoadConfiguration = (name: string) => {
    try {
      const config = loadConfiguration(name);
      if (config) {
        setFormState({
          secondaryPack: config.secondaryPack,
          primaryPacks: config.primaryPacks.map(pack => ({
            ...pack,
            name: pack.name || `Pack ${pack.id}` // Ensure name property exists
          })),
          algorithmType: config.algorithmType,
          algorithmSettings: config.algorithmSettings,
        });
      } else {
        setError(`Configuration '${name}' not found`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load configuration');
    }
  };

  // Handle delete configuration
  const handleDeleteConfiguration = (name: string) => {
    try {
      deleteConfiguration(name);
      refreshSavedConfigurations();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete configuration');
    }
  };

  // Handle export configuration
  const handleExportConfiguration = (name: string) => {
    try {
      const config = loadConfiguration(name);
      if (config) {
        exportConfigurationToFile(config);
      } else {
        setError(`Configuration '${name}' not found`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export configuration');
    }
  };

  // Handle import configuration
  const handleImportConfiguration = async (file: File): Promise<void> => {
    try {
      const config = await importConfigurationFromFile(file);
      saveConfiguration(config);
      refreshSavedConfigurations();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import configuration');
      throw err;
    }
  };

  return {
    formState,
    setFormState,
    packingResult,
    isLoading,
    error,
    handlePack,
    handleIndustryPack,
    handleSaveConfiguration,
    handleLoadConfiguration,
    handleDeleteConfiguration,
    handleExportConfiguration,
    handleImportConfiguration,
    savedConfigurations,
    refreshSavedConfigurations,
    setSelectedIndustryData,
  };
}
