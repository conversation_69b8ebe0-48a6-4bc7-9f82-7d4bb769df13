package com.packprime.innovademo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.packprime.innovademo.model.RuleTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing rule templates and user rules.
 */
@Service
public class RuleService {
    private static final Logger log = LoggerFactory.getLogger(RuleService.class);
    private static final String INDUSTRY_DATA_PATH = "classpath:static/industry_data/*.json";
    private static final String USER_RULES_DIR = "user_rules";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private final ResourceLoader resourceLoader;
    
    public RuleService(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
        // Ensure user rules directory exists
        createUserRulesDirectoryIfNotExists();
    }
    
    /**
     * Creates the user rules directory if it doesn't exist.
     */
    private void createUserRulesDirectoryIfNotExists() {
        Path userRulesPath = Paths.get(USER_RULES_DIR);
        if (!Files.exists(userRulesPath)) {
            try {
                Files.createDirectories(userRulesPath);
                log.info("Created user rules directory: {}", userRulesPath.toAbsolutePath());
            } catch (IOException e) {
                log.error("Failed to create user rules directory: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * Gets a list of all built-in rule templates.
     * 
     * @return List of rule templates
     */
    public List<RuleTemplate> getBuiltInTemplates() {
        List<RuleTemplate> templates = new ArrayList<>();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        
        try {
            Resource[] resources = resolver.getResources(INDUSTRY_DATA_PATH);
            
            for (Resource resource : resources) {
                String filename = resource.getFilename();
                if (filename != null && !filename.isEmpty()) {
                    // Only include files that start with "ffdv_" for FirstFitDecreasingVolume tests
                    if (filename.startsWith("ffdv_")) {
                        RuleTemplate template = new RuleTemplate();
                        template.setId(filename);
                        template.setFilename(filename);
                        template.setSource("builtin");
                        
                        // Try to extract template metadata
                        try (InputStream inputStream = resource.getInputStream()) {
                            JsonNode rootNode = objectMapper.readTree(inputStream);
                            if (rootNode.has("name")) {
                                template.setName(rootNode.get("name").asText());
                            } else {
                                template.setName(filename.replace(".json", "").replace("_", " "));
                            }
                            
                            if (rootNode.has("description")) {
                                template.setDescription(rootNode.get("description").asText());
                            } else {
                                template.setDescription("Rule template for " + filename);
                            }
                            
                            // Add algorithm type
                            if (rootNode.has("algorithmType")) {
                                template.setAlgorithmType(rootNode.get("algorithmType").asText());
                            } else {
                                template.setAlgorithmType("FirstFitDecreasingVolume");
                            }
                        } catch (Exception e) {
                            log.warn("Could not parse JSON file {}: {}", filename, e.getMessage());
                            template.setName(filename.replace(".json", "").replace("_", " "));
                            template.setDescription("Rule template for " + filename);
                            template.setAlgorithmType("FirstFitDecreasingVolume");
                        }
                        
                        templates.add(template);
                    }
                }
            }
        } catch (IOException e) {
            log.error("Error accessing rule templates: {}", e.getMessage(), e);
        }
        
        return templates;
    }
    
    /**
     * Gets a list of all user-created rule templates.
     * 
     * @return List of user rule templates
     */
    public List<RuleTemplate> getUserRules() {
        List<RuleTemplate> userRules = new ArrayList<>();
        Path userRulesPath = Paths.get(USER_RULES_DIR);
        
        try {
            if (Files.exists(userRulesPath) && Files.isDirectory(userRulesPath)) {
                List<Path> jsonFiles = Files.list(userRulesPath)
                        .filter(path -> path.toString().endsWith(".json"))
                        .collect(Collectors.toList());
                
                for (Path jsonFile : jsonFiles) {
                    File file = jsonFile.toFile();
                    String filename = file.getName();
                    
                    RuleTemplate template = new RuleTemplate();
                    template.setId(filename);
                    template.setFilename(filename);
                    template.setSource("user");
                    template.setCreatedAt(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .format(new Date(file.lastModified())));
                    template.setLastModified(template.getCreatedAt());
                    
                    // Try to extract template metadata
                    try {
                        JsonNode rootNode = objectMapper.readTree(file);
                        if (rootNode.has("name")) {
                            template.setName(rootNode.get("name").asText());
                        } else {
                            template.setName(filename.replace(".json", "").replace("_", " "));
                        }
                        
                        if (rootNode.has("description")) {
                            template.setDescription(rootNode.get("description").asText());
                        } else {
                            template.setDescription("User rule template");
                        }
                        
                        // Add algorithm type
                        if (rootNode.has("algorithmType")) {
                            template.setAlgorithmType(rootNode.get("algorithmType").asText());
                        } else {
                            template.setAlgorithmType("FirstFitDecreasingVolume");
                        }
                    } catch (Exception e) {
                        log.warn("Could not parse JSON file {}: {}", filename, e.getMessage());
                        template.setName(filename.replace(".json", "").replace("_", " "));
                        template.setDescription("User rule template");
                        template.setAlgorithmType("FirstFitDecreasingVolume");
                    }
                    
                    userRules.add(template);
                }
            }
        } catch (IOException e) {
            log.error("Error accessing user rules: {}", e.getMessage(), e);
        }
        
        return userRules;
    }
    
    /**
     * Gets the content of a built-in template by ID.
     * 
     * @param id Template ID
     * @return Template content as JsonNode
     * @throws IOException If template cannot be read
     */
    public JsonNode getBuiltInTemplateContent(String id) throws IOException {
        Resource resource = resourceLoader.getResource("classpath:static/industry_data/" + id);
        if (resource.exists()) {
            try (InputStream inputStream = resource.getInputStream()) {
                return objectMapper.readTree(inputStream);
            }
        }
        throw new IOException("Template not found: " + id);
    }
    
    /**
     * Gets the content of a user rule by ID.
     * 
     * @param id Rule ID
     * @return Rule content as JsonNode
     * @throws IOException If rule cannot be read
     */
    public JsonNode getUserRuleContent(String id) throws IOException {
        Path rulePath = Paths.get(USER_RULES_DIR, id);
        if (Files.exists(rulePath)) {
            return objectMapper.readTree(rulePath.toFile());
        }
        throw new IOException("User rule not found: " + id);
    }
    
    /**
     * Saves a user rule.
     * 
     * @param name Rule name
     * @param content Rule content as JsonNode
     * @return Saved rule template
     * @throws IOException If rule cannot be saved
     */
    public RuleTemplate saveUserRule(String name, JsonNode content) throws IOException {
        // Generate a unique filename
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String sanitizedName = name.replaceAll("[^a-zA-Z0-9]", "_").toLowerCase();
        String filename = "user_" + timestamp + "_" + sanitizedName + ".json";
        Path rulePath = Paths.get(USER_RULES_DIR, filename);
        
        // Ensure the content has a name field
        if (content.isObject() && !content.has("name")) {
            ((com.fasterxml.jackson.databind.node.ObjectNode) content).put("name", name);
        }
        
        // Write the content to the file
        try (FileWriter writer = new FileWriter(rulePath.toFile())) {
            writer.write(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(content));
        }
        
        // Create and return the rule template
        RuleTemplate template = new RuleTemplate();
        template.setId(filename);
        template.setName(name);
        template.setFilename(filename);
        template.setSource("user");
        template.setCreatedAt(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        template.setLastModified(template.getCreatedAt());
        
        if (content.has("description")) {
            template.setDescription(content.get("description").asText());
        } else {
            template.setDescription("User rule template");
        }
        
        if (content.has("algorithmType")) {
            template.setAlgorithmType(content.get("algorithmType").asText());
        } else {
            template.setAlgorithmType("FirstFitDecreasingVolume");
        }
        
        return template;
    }
    
    /**
     * Deletes a user rule by ID.
     * 
     * @param id Rule ID
     * @return true if rule was deleted, false otherwise
     */
    public boolean deleteUserRule(String id) {
        Path rulePath = Paths.get(USER_RULES_DIR, id);
        try {
            return Files.deleteIfExists(rulePath);
        } catch (IOException e) {
            log.error("Failed to delete user rule {}: {}", id, e.getMessage(), e);
            return false;
        }
    }
}
