# Packing Algorithm Visualization Frontend Project

## Project Overview
Create a Next.js frontend for visualizing packing algorithms that calculate solutions for placing primary packs into secondary packs. The application will use shadcn/ui components for the UI and Three.js for 3D visualization.

## Requirements
- Single Page Application (SPA)
- Blue, dark, and white theme
- Support for saving/loading configurations as JSON files
- Tables for displaying primary packs and packing results
- 3D visualization with solid colors and semi-transparent secondary pack

## Tasks

### 1. Project Setup
- [x] Define project structure and requirements
- [x] Create frontend directory in the project
- [x] Initialize Next.js project
- [x] Install and configure Tailwind CSS
- [x] Install and configure shadcn/ui
- [x] Create ShadCN-context.md to track used components
- [x] Install Three.js and related dependencies

### 2. UI Components
- [x] Create layout with split view (forms on left, visualization on right)
- [x] Implement Secondary Pack form section
  - [x] Input fields for length, width, height (mm)
  - [x] Input field for max weight (kg)
- [x] Implement Primary Packs form section
  - [x] Dynamic form for multiple primary packs
  - [x] Fields for name, dimensions, weight, and quantity
  - [x] Add/remove functionality
- [x] Create table for displaying primary packs to be packed
- [x] Create table for displaying packing results
- [x] Implement Pack button and loading states
- [x] Add save/load configuration functionality

### 3. Three.js Visualization
- [x] Set up Three.js scene, camera, and renderer
- [x] Create secondary pack (container) visualization
- [x] Implement visualization for placed primary packs
- [x] Add camera controls for rotating, panning, and zooming
- [x] Implement color coding for different primary pack types
- [x] Add semi-transparency for the secondary pack

### 4. API Integration
- [x] Create service for API communication
- [x] Implement data transformation to match backend DTOs
- [x] Connect form submission to API endpoints
- [x] Handle API responses and errors
- [x] Update visualization based on API response

### 5. State Management
- [x] Implement state management for form data
- [x] Create state for packing results
- [x] Handle loading and error states
- [x] Implement save/load functionality for configurations

### 6. Styling and Theming
- [x] Apply blue, dark, and white theme
- [x] Ensure responsive design for different screen sizes
- [x] Add appropriate spacing and layout
- [x] Implement dark mode toggle

### 7. Testing and Optimization
- [x] Create Visual Tests page for algorithm testing (/visual-tests)
- [x] Implement test results visualization with bin dimensions display
- [x] Add JSON request configuration viewer
- [x] Fix volume utilization display
- [ ] Test form validation
- [ ] Test API integration
- [x] Test 3D visualization with different packing scenarios
- [ ] Optimize performance for large packing solutions
- [ ] Ensure accessibility compliance

### 8. Documentation
- [ ] Document code with appropriate comments
- [x] Create README with setup and usage instructions
- [ ] Document API integration points
- [ ] Create ShadCN-context.md with list of used components

## Technology Stack
- Next.js
- Tailwind CSS
- shadcn/ui components
- Three.js
- React Hooks for state management

## Project Structure

### Frontend Structure
- `/frontend/src/app` - Next.js app router pages
  - `/page.tsx` - Main application page with packing form and visualization
  - `/visual-tests/page.tsx` - Page for running predefined visual tests of packing algorithms
- `/frontend/src/components` - Reusable UI components
  - `/forms` - Form components for data input
  - `/tables` - Table components for displaying results
  - `/three` - Three.js visualization components
  - `/layout` - Layout components like main-layout.tsx
  - `/ui` - shadcn/ui components
  - `/theme` - Theme-related components

### Backend Structure
- `/src/main/java/com/packprime/innovademo` - Java backend code
  - `/algorithm` - Packing algorithm implementations
  - `/controller` - REST API controllers
  - `/dto` - Data Transfer Objects
  - `/service` - Business logic services
- `/src/test/java/com/packprime/innovademo` - Test classes
  - `VisualPackingTest.java` - Tests for visual packing scenarios
  - `FirstFitDecreasingVolumeAlgorithmTest.java` - Tests for FFDV algorithm
  - `MaximalRectanglesAlgorithmTest.java` - Tests for Maximal Rectangles algorithm

## Pages
- `/` - Main application page with packing form and visualization
- `/visual-tests` - Page for running predefined visual tests of packing algorithms

## Navigation
- The main page (/) includes a link to the Visual Tests page in the header
- The Visual Tests page includes a "Back to Home" button to return to the main page

## shadcn/ui Components Used
- Card, CardHeader, CardContent, CardFooter, CardDescription, CardTitle
- Input
- Button
- Form components (Form, FormField, FormItem, FormLabel, FormControl)
- Table components
- Tabs, TabsList, TabsTrigger, TabsContent
- Alert, AlertDescription, AlertTitle
- Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger
- Separator
- Skeleton
- Select, SelectTrigger, SelectValue, SelectContent, SelectItem

## API Endpoints
- `/api/packing/pack` - Main endpoint for packing algorithm
- `/api/packing/pack-industry` - Alternative endpoint for industry-specific packing
- `/api/visual-tests` - Endpoint to fetch available visual tests
- `/api/industry-data/{testId}` - Endpoint to fetch specific test data
