package com.packprime.innovademo.algorithm;

import com.packprime.innovademo.dto.PackingRequest; // Import for AlgorithmSettingsDto
import com.packprime.innovademo.model.BinDefinition; // Import for Bin constraints
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map; // Added for grouping
import java.util.stream.Collectors; // Needed for grouping and unplaced pieces
import org.slf4j.Logger; // Added logger
import org.slf4j.LoggerFactory; // Added logger
import org.springframework.stereotype.Component;

/**
 * A simple First Fit Decreasing Volume packing algorithm. Sorts pieces by priority/volume and
 * places each piece in the first valid spot found. Does not yet implement full constraint checking.
 */
@Component("FFDV") // First Fit Decreasing Volume
public class FirstFitDecreasingVolumeAlgorithm implements PackingAlgorithm {

  private static final Logger logger =
      LoggerFactory.getLogger(FirstFitDecreasingVolumeAlgorithm.class); // Added logger

  @Override
  public PackingResult pack(
      Bin bin,
      List<Piece> pieces,
      BinDefinition binDefinition,
      PackingRequest.AlgorithmSettingsDto settings) {
    logger.info(
        "Starting FFDV Algorithm. Pieces: {}, Bin: {}x{}x{}, MaxWeight: {}",
        pieces.size(),
        bin.getWidth(),
        bin.getHeight(),
        bin.getDepth(),
        binDefinition.getMaxWeight());
    bin.reset(); // Ensure bin is empty
    List<Piece> allPieces = new ArrayList<>(pieces);
    allPieces.forEach(Piece::reset); // Ensure all pieces are reset
    List<String> statusMessages = new ArrayList<>();

    // Sort pieces: Prioritize by loadPriority (descending), then by volume (descending)
    logger.debug("Sorting {} pieces by priority/volume...", allPieces.size());
    allPieces.sort(
        Comparator.comparingInt(Piece::getLoadPriority)
            .reversed()
            .thenComparing(Comparator.comparingDouble(Piece::getVolume).reversed()));
    logger.debug("Sorting complete.");

    // Target Percentage Handling
    double targetUtilization = -1.0; // Default to no target
    if (settings != null && settings.getTargetPercentage() != null) {
      double requestedTarget = settings.getTargetPercentage();
      if (requestedTarget > 0 && requestedTarget <= 100) {
        targetUtilization = requestedTarget;
        logger.info("Target volume utilization set to: {}%", targetUtilization);
      } else {
        logger.warn("Invalid targetPercentage ({}) provided. Ignoring target.", requestedTarget);
        statusMessages.add("Warning: Invalid targetPercentage (" + requestedTarget + ") ignored.");
      }
    }

    // Vertical Orientation Constraint Handling
    VerticalOrientationConstraint verticalConstraint = 
        settings != null && settings.getVerticalConstraint() != null 
        ? settings.getVerticalConstraint() 
        : VerticalOrientationConstraint.HEIGHT_ONLY;
    logger.info("Vertical orientation constraint set to: {}", verticalConstraint);

    List<Piece> placedPieces = new ArrayList<>(); // Track placed pieces
    int pieceIndex = 0; // Counter for logging
    double currentVolumeUtilization = 0.0; // Track current utilization

    for (Piece piece : allPieces) {
      pieceIndex++;
      logger.trace(
          "Attempting to place piece {}/{} ({})", pieceIndex, allPieces.size(), piece.getId());
      boolean isPlaced = false;
      // Iterate through corners (order might matter, using sorted list for determinism)
      List<Position> sortedCorners = new ArrayList<>(bin.getCorners());
      sortedCorners.sort(
          Comparator.comparingDouble(Position::getX)
              .thenComparingDouble(Position::getY)
              .thenComparingDouble(Position::getZ));
      logger.trace("Piece {}: Checking {} corners.", piece.getId(), sortedCorners.size());

      for (Position corner : sortedCorners) {
        // Iterate through allowed orientations
        for (int orientation = 0; orientation < 6; orientation++) {
          // 1. Check Piece Orientation Constraint
          if (!piece.isOrientationAllowed(orientation)) {
            logger.trace(
                "Piece {}: Skipping orientation {}: Not allowed by piece.",
                piece.getId(),
                orientation);
            continue;
          }

          // 2. Check Vertical Orientation Constraint
          if (!isOrientationAllowedByVerticalConstraint(orientation, verticalConstraint)) {
            logger.trace(
                "Piece {}: Skipping orientation {}: Not allowed by vertical constraint setting {}.",
                piece.getId(),
                orientation,
                verticalConstraint);
            continue;
          }

          // 3. Check Placement Feasibility (Needs full constraint check)
          List<String> checkMessages =
              new ArrayList<>(); // Use temporary list for this specific check
          boolean canPlace =
              bin.checkPlacement(
                  piece, corner, orientation, binDefinition, settings, checkMessages);
          // TODO: The checkPlacement method in Bin.java needs to be updated
          // to actually use binDefinition and settings for checks (weight, overhang etc.)

          if (canPlace) {
            logger.debug(
                "Piece {} ({}/{}): Placing at {} (Orient {})",
                piece.getId(),
                pieceIndex,
                allPieces.size(),
                corner,
                orientation);
            // Place the piece at the first valid spot found
            bin.placePiece(piece, corner, orientation);
            placedPieces.add(piece); // Add to our list of placed pieces
            isPlaced = true;

            // Check target utilization after placing the piece
            if (targetUtilization > 0 && bin.getVolume() > 1e-9) {
              double totalPlacedVolume =
                  bin.getPlacedPieces().stream().mapToDouble(Piece::getVolume).sum();
              currentVolumeUtilization = (totalPlacedVolume / bin.getVolume()) * 100.0;
              logger.trace("Current volume utilization: {:.2f}%", currentVolumeUtilization);
              // We check the condition to break *outside* the inner loops,
              // after processing the current piece fully.
            }
            break; // Break from orientation loop
          } else {
            logger.trace(
                "Piece {}: Placement check FAILED at {} (Orient {}): {}",
                piece.getId(),
                corner,
                orientation,
                String.join("; ", checkMessages));
            // Optionally add first failure reason to main statusMessages?
            // if (!checkMessages.isEmpty()) {
            //    statusMessages.add(checkMessages.get(0));
            // }
          }
        }
        if (isPlaced) {
          break; // Break from corner loop
        }
      }
      if (!isPlaced) {
        logger.warn(
            "Piece {} ({}/{}): Could not find valid placement.",
            piece.getId(),
            pieceIndex,
            allPieces.size());
        // Message added later if unplacedPieces is not empty
      }

      // Check if target utilization was met after processing this piece
      if (targetUtilization > 0 && currentVolumeUtilization >= targetUtilization) {
        logger.info(
            "Target volume utilization ({:.2f}%) reached or exceeded ({:.2f}%). Stopping packing.",
            targetUtilization, currentVolumeUtilization);
        statusMessages.add(
            String.format(
                "Packing stopped: Target volume utilization (%.2f%%) reached (%.2f%%).",
                targetUtilization, currentVolumeUtilization));
        break; // Stop processing further pieces
      }
    }

    // Determine unplaced pieces
    List<Piece> unplacedPieces =
        allPieces.stream().filter(p -> !placedPieces.contains(p)).collect(Collectors.toList());

    if (!unplacedPieces.isEmpty()) {
      String unplacedMsg = "Could not place " + unplacedPieces.size() + " pieces.";
      statusMessages.add(unplacedMsg);
      logger.warn(
          "{}: {}",
          unplacedMsg,
          unplacedPieces.stream().map(Piece::getId).collect(Collectors.joining(", ")));
    }

    // Post-packing checks for min/max quantity constraints
    logger.debug("Performing post-packing min/max quantity checks...");
    Map<String, Long> placedCounts =
        placedPieces.stream().collect(Collectors.groupingBy(Piece::getId, Collectors.counting()));

    Map<String, Piece> originalPieceMap =
        allPieces.stream()
            .collect(
                Collectors.toMap(
                    Piece::getId,
                    p -> p,
                    (p1, p2) -> {
                      logger.warn(
                          "Duplicate piece ID '{}' found in input list during post-check mapping."
                              + " Using the first encountered.",
                          p1.getId());
                      return p1;
                    })); // Handle potential duplicate IDs

    originalPieceMap.forEach(
        (id, originalPiece) -> {
          long count = placedCounts.getOrDefault(id, 0L);
          if (originalPiece.getMinQuantity() > 0 && count < originalPiece.getMinQuantity()) {
            String message =
                String.format(
                    "Constraint Violation (FFDV): Minimum quantity for piece %s not met (placed %d,"
                        + " required %d)",
                    id, count, originalPiece.getMinQuantity());
            statusMessages.add(message);
            logger.warn(message);
          }
          if (originalPiece.getMaxQuantity() < Integer.MAX_VALUE
              && count > originalPiece.getMaxQuantity()) {
            String message =
                String.format(
                    "Constraint Violation (FFDV): Maximum quantity for piece %s exceeded (placed"
                        + " %d, max %d)",
                    id, count, originalPiece.getMaxQuantity());
            statusMessages.add(message);
            logger.warn(message);
          }
        });
    logger.debug("Post-packing checks complete.");

    logger.info(
        "FFDV Algorithm finished. Placed: {}, Unplaced: {}, Messages: {}",
        placedPieces.size(),
        unplacedPieces.size(),
        statusMessages.size());
    return new PackingResult(placedPieces, unplacedPieces, statusMessages);
  }
  
  /**
   * Determines if an orientation is allowed based on the vertical constraint setting.
   *
   * @param orientation The orientation to check (0-5)
   * @param verticalConstraint The vertical constraint setting
   * @return true if the orientation satisfies the vertical constraint
   */
  private boolean isOrientationAllowedByVerticalConstraint(
      int orientation, VerticalOrientationConstraint verticalConstraint) {
    
    // If no constraint specified, default to HEIGHT_ONLY
    if (verticalConstraint == null) {
      verticalConstraint = VerticalOrientationConstraint.HEIGHT_ONLY;
    }
    
    // Map orientation to which dimension is vertical (Y-axis)
    // In the standard orientation system:
    // 0: Original (Height is vertical)
    // 1: Rotated Z 90° (Height is vertical)
    // 2: Rotated Y 90° (Width is vertical)
    // 3: Rotated Y 90°, then Z 90° (Width is vertical)
    // 4: Rotated X 90° (Length is vertical)
    // 5: Rotated X 90°, then Z 90° (Length is vertical)
    
    String verticalDimension = switch (orientation) {
      case 0, 1 -> "HEIGHT";
      case 2, 3 -> "WIDTH";
      case 4, 5 -> "LENGTH";
      default -> "UNKNOWN";
    };
    
    return switch (verticalConstraint) {
      case LENGTH_ONLY -> verticalDimension.equals("LENGTH");
      case WIDTH_ONLY -> verticalDimension.equals("WIDTH");
      case HEIGHT_ONLY -> verticalDimension.equals("HEIGHT");
      case HEIGHT_OR_WIDTH -> verticalDimension.equals("HEIGHT") || verticalDimension.equals("WIDTH");
      case HEIGHT_OR_LENGTH -> verticalDimension.equals("HEIGHT") || verticalDimension.equals("LENGTH");
      case WIDTH_OR_LENGTH -> verticalDimension.equals("WIDTH") || verticalDimension.equals("LENGTH");
      case ANY -> true;
    };
  }
}
