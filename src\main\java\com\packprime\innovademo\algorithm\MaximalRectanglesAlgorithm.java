package com.packprime.innovademo.algorithm;

import com.packprime.innovademo.dto.PackingRequest;
import com.packprime.innovademo.dto.PackingRequest.AlgorithmSettingsDto;
import com.packprime.innovademo.model.BinDefinition;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Packing algorithm based on the Maximal Rectangles (Maximal Empty Spaces) heuristic.
 */
@Component("MaximalRectangles")
public class MaximalRectanglesAlgorithm implements PackingAlgorithm {

    private static final Logger logger = LoggerFactory.getLogger(MaximalRectanglesAlgorithm.class);

    // Represents a maximal empty rectangular space within the bin
    private record MaximalEmptySpace(
        double x, double y, double z, double width, double height, double depth) {

        Position getPosition() { return new Position(x, y, z); }

        // Check if this space completely contains another space (with tolerance)
        boolean contains(MaximalEmptySpace other) {
            double tol = 1e-6;
            return x <= other.x + tol && y <= other.y + tol && z <= other.z + tol &&
                   x + width >= other.x + other.width - tol &&
                   y + height >= other.y + other.height - tol &&
                   z + depth >= other.z + other.depth - tol;
        }
         // Check for intersection (overlap) with another space
        boolean intersects(MaximalEmptySpace other) {
            return x < other.x + other.width && x + width > other.x &&
                   y < other.y + other.height && y + height > other.y &&
                   z < other.z + other.depth && z + depth > other.z;
        }
    }

    // Method signature matching the PackingAlgorithm interface
    @Override
    public PackingResult pack(
        Bin bin,
        List<Piece> pieces,
        BinDefinition binDefinition,
        AlgorithmSettingsDto settings) {
        
        logger.info("Starting Maximal Rectangles packing for bin {}x{}x{} with {} pieces.",
                    bin.getWidth(), bin.getHeight(), bin.getDepth(), pieces.size());

        List<Piece> unpackedPieces = new ArrayList<>(pieces);
        // Sort pieces by volume descending
        unpackedPieces.sort(Comparator.comparingDouble(Piece::getVolume).reversed());

        List<MaximalEmptySpace> mesList = new ArrayList<>();
        mesList.add(new MaximalEmptySpace(0, 0, 0, bin.getWidth(), bin.getHeight(), bin.getDepth()));

        while (!unpackedPieces.isEmpty() && !mesList.isEmpty()) {
            MaximalEmptySpace bestMES = null;
            Piece bestPiece = null;
            Position bestPosition = null;
            int bestOrientation = -1;
            int bestPieceIndex = -1;
            double bestScore = Double.MAX_VALUE; // Lower score is better (e.g., min z, then y, then x)

            // Heuristic: Choose the best MES to try placing into.
            // Simple approach: try the one with min z, then min y, then min x.
             mesList.sort(
                 Comparator.comparingDouble(MaximalEmptySpace::z)
                     .thenComparingDouble(MaximalEmptySpace::y)
                     .thenComparingDouble(MaximalEmptySpace::x));
            
            MaximalEmptySpace chosenMES = mesList.get(0); // Try the 'best' MES first

            // Try to find the best fit piece for the chosen MES
            for (int i = 0; i < unpackedPieces.size(); i++) {
                Piece currentPiece = unpackedPieces.get(i);
                double width = currentPiece.getWidth();
                double height = currentPiece.getHeight();
                double depth = currentPiece.getDepth();

                for (int orientation = 0; orientation < 6; orientation++) {
                    // Get oriented dimensions based on orientation
                    double[] orientedDimensions = getOrientedDimensions(width, height, depth, orientation);
                    double orientedWidth = orientedDimensions[0];
                    double orientedHeight = orientedDimensions[1];
                    double orientedDepth = orientedDimensions[2];
                    
                    Position placementPosition = chosenMES.getPosition(); 
                    double tol = 1e-6;

                    // Check if dimensions fit within the chosen MES
                    if (orientedWidth <= chosenMES.width() + tol &&
                        orientedHeight <= chosenMES.height() + tol &&
                        orientedDepth <= chosenMES.depth() + tol) {
                        
                        // Create a temporary piece dimensions object for collision checking
                        Piece.Dimensions tempDims = new Piece.Dimensions(orientedWidth, orientedHeight, orientedDepth);
                        
                        // Check for collision with already placed items in the bin
                        boolean hasCollision = false;
                        for (Piece placed : bin.getPlacedPieces()) {
                            Piece.Dimensions placedDims = placed.getDimensionsForOrientation(placed.getOrientation());
                            Box placedBox = new Box(placed.getPosition(), placedDims);
                            Box newBox = new Box(placementPosition, tempDims);
                            
                            if (boxesOverlap(newBox, placedBox)) {
                                hasCollision = true;
                                break;
                            }
                        }
                        
                        if (!hasCollision) { 
                            // Check weight constraint BEFORE considering it the best fit
                            if (bin.getCurrentWeight() + currentPiece.getWeight() <= binDefinition.getMaxWeight()) {
                                // Found a valid placement in this MES
                                // Update if this is the first valid placement found for this MES
                                // (Could add more sophisticated scoring here later)
                                if(bestPiece == null) { // Prioritize first fit for simplicity now
                                    bestMES = chosenMES;
                                    bestPiece = currentPiece;
                                    bestPosition = placementPosition;
                                    bestOrientation = orientation;
                                    bestPieceIndex = i;
                                    logger.trace("Found potential placement in MES {}: Piece {} at {} (Orient {}) Weight OK", 
                                                bestMES, bestPiece.getId(), bestPosition, bestOrientation);
                                    break; // Found the first fit piece for this MES
                                }
                            } else {
                                logger.trace("Skipping piece {} for MES {}: Exceeds max weight ({} + {} > {})",
                                            currentPiece.getId(), chosenMES, bin.getCurrentWeight(), currentPiece.getWeight(), binDefinition.getMaxWeight());
                            }
                        }
                    }
                }
                 if(bestPiece != null) break; // Stop checking pieces once one fits in the chosen MES
            }

            if (bestPiece != null) {
                // 1. Place the selected piece
                logger.debug("Placing piece {} (Index {}) at {}, orientation {} into MES {}",
                             bestPiece.getId(), bestPieceIndex, bestPosition, bestOrientation, bestMES);
                
                // Use the placePiece method from Bin class
                bin.placePiece(bestPiece, bestPosition, bestOrientation); 
                unpackedPieces.remove(bestPieceIndex);

                // 2. Update MES list
                double[] orientedDimensions = getOrientedDimensions(
                    bestPiece.getWidth(), bestPiece.getHeight(), bestPiece.getDepth(), bestOrientation);
                
                updateMESList(mesList, bestMES, 
                    orientedDimensions[0], orientedDimensions[1], orientedDimensions[2], bestPosition); 

            } else {
                 // No piece could be placed in the chosen MES. Remove it and try again.
                 logger.trace("No piece fits in MES {}. Removing it.", chosenMES);
                 mesList.remove(0); // Remove the MES we couldn't place anything into
                 if(mesList.isEmpty()) {
                     logger.info("No usable Maximal Empty Spaces remaining.");
                 }
            }
        }

        if(unpackedPieces.isEmpty()){
            logger.info("Successfully placed all pieces.");
        } else {
             logger.info("Packing finished. {} pieces could not be placed.", unpackedPieces.size());
        }

        logger.info("Final bin state: {} pieces placed. Volume Utilisation: {:.2f}%",
                    bin.getPlacedPieces().size(), bin.getVolumeUtilisation()); 
                    
        // Create and return the PackingResult
        return new PackingResult(bin.getPlacedPieces(), unpackedPieces);
    }
    
    // Helper method to get oriented dimensions based on orientation
    private double[] getOrientedDimensions(double width, double height, double depth, int orientation) {
        double[] dimensions = new double[3];
        
        switch (orientation) {
            case 0: // Original orientation (w,h,d)
                dimensions[0] = width;
                dimensions[1] = height;
                dimensions[2] = depth;
                break;
            case 1: // Rotate 90° around Z (d,h,w)
                dimensions[0] = depth;
                dimensions[1] = height;
                dimensions[2] = width;
                break;
            case 2: // Rotate 90° around Y (h,w,d)
                dimensions[0] = height;
                dimensions[1] = width;
                dimensions[2] = depth;
                break;
            case 3: // Rotate 90° around X (w,d,h)
                dimensions[0] = width;
                dimensions[1] = depth;
                dimensions[2] = height;
                break;
            case 4: // Rotate 90° around Z, then 90° around X (d,w,h)
                dimensions[0] = depth;
                dimensions[1] = width;
                dimensions[2] = height;
                break;
            case 5: // Rotate 90° around Y, then 90° around Z (h,d,w)
                dimensions[0] = height;
                dimensions[1] = depth;
                dimensions[2] = width;
                break;
        }
        
        return dimensions;
    }
    
    // Helper method to check if two boxes overlap
    private boolean boxesOverlap(Box box1, Box box2) {
        double EPSILON = 1e-6;
        
        // Check for separation along each axis
        // Return false (no overlap) if they are separated on any axis
        if (box1.maxX <= box2.minX + EPSILON || box1.minX >= box2.maxX - EPSILON) return false; // X-axis separation
        if (box1.maxY <= box2.minY + EPSILON || box1.minY >= box2.maxY - EPSILON) return false; // Y-axis separation
        if (box1.maxZ <= box2.minZ + EPSILON || box1.minZ >= box2.maxZ - EPSILON) return false; // Z-axis separation

        // If they are not separated along any axis, they must overlap
        return true;
    }
    
    // Helper class for representing a box
    private static class Box {
        final double minX, minY, minZ;
        final double maxX, maxY, maxZ;
        
        Box(Position corner, Piece.Dimensions dims) {
            this.minX = corner.getX();
            this.minY = corner.getY();
            this.minZ = corner.getZ();
            this.maxX = corner.getX() + dims.w;
            this.maxY = corner.getY() + dims.h;
            this.maxZ = corner.getZ() + dims.d;
        }
    }

    // --- Updated updateMESList Logic ---
    private void updateMESList(
        List<MaximalEmptySpace> mesList, 
        MaximalEmptySpace usedMES, 
        double placedWidth, 
        double placedHeight, 
        double placedDepth, 
        Position placedPosition) {
        
        logger.trace("Updating MES list due to piece placement at {} in used MES {}", placedPosition, usedMES);

        // Remove the exact MES that was used for placement
         mesList.removeIf(mes -> 
            Math.abs(mes.x - usedMES.x) < 1e-6 && Math.abs(mes.y - usedMES.y) < 1e-6 && Math.abs(mes.z - usedMES.z) < 1e-6 &&
            Math.abs(mes.width - usedMES.width) < 1e-6 && Math.abs(mes.height - usedMES.height) < 1e-6 && Math.abs(mes.depth - usedMES.depth) < 1e-6
        );
        
        List<MaximalEmptySpace> candidates = new ArrayList<>();
        double tol = 1e-6;

        // Generate up to 6 candidates by splitting the used MES around the placed box
        double px = placedPosition.getX();
        double py = placedPosition.getY();
        double pz = placedPosition.getZ();
        double pw = placedWidth;
        double ph = placedHeight;
        double pd = placedDepth;
        
        double ux = usedMES.x();
        double uy = usedMES.y();
        double uz = usedMES.z();
        double uw = usedMES.width();
        double uh = usedMES.height();
        double ud = usedMES.depth();

        // X-splits
        if (px > ux + tol) candidates.add(new MaximalEmptySpace(ux, uy, uz, px - ux, uh, ud));
        if (px + pw < ux + uw - tol) candidates.add(new MaximalEmptySpace(px + pw, uy, uz, ux + uw - (px + pw), uh, ud));
        // Y-splits (within X-range of the *original* used MES, not just placed box)
        if (py > uy + tol) candidates.add(new MaximalEmptySpace(ux, uy, uz, uw, py - uy, ud));
        if (py + ph < uy + uh - tol) candidates.add(new MaximalEmptySpace(ux, py + ph, uz, uw, uy + uh - (py + ph), ud));
        // Z-splits (within XY-range of the *original* used MES)
        if (pz > uz + tol) candidates.add(new MaximalEmptySpace(ux, uy, uz, uw, uh, pz - uz));
        if (pz + pd < uz + ud - tol) candidates.add(new MaximalEmptySpace(ux, uy, pz + pd, uw, uh, uz + ud - (pz + pd)));

         logger.trace("Generated {} raw candidates from splitting used MES.", candidates.size());

     // Now, refine the candidate list by processing intersections with *other* existing MES
     List<MaximalEmptySpace> finalNewSpaces = new ArrayList<>();

     for (MaximalEmptySpace candidate : candidates) {
          if (candidate.width() < tol || candidate.height() < tol || candidate.depth() < tol) continue; // Skip zero volume

         List<MaximalEmptySpace> intersectingObstacles = new ArrayList<>();
           for(MaximalEmptySpace existing : mesList) {
               if(candidate.intersects(existing)) {
                  intersectingObstacles.add(existing);
               }
           }

         List<MaximalEmptySpace> currentFragments = new ArrayList<>();
         currentFragments.add(candidate); // Start with the candidate itself

         // Iteratively subtract each intersecting obstacle from the fragments
         for (MaximalEmptySpace obstacle : intersectingObstacles) {
             List<MaximalEmptySpace> nextFragments = new ArrayList<>();
             for (MaximalEmptySpace frag : currentFragments) {
                 if (frag.intersects(obstacle)) {
                     // Subtract the obstacle from the fragment and add resulting pieces
                     nextFragments.addAll(subtractObstacle(frag, obstacle)); 
                 } else {
                     // This fragment doesn't intersect the current obstacle, keep it
                     nextFragments.add(frag); 
                 }
             }
             currentFragments = nextFragments; // Update fragments for the next obstacle
         }

         // After processing all obstacles, currentFragments contains the valid parts of the original candidate
         // Add all these potentially valid fragments to the final list temporarily.
         // The final maximality check at the end will clean up redundancies.
         for (MaximalEmptySpace finalFragment : currentFragments) {
             if (finalFragment.width() < tol || finalFragment.height() < tol || finalFragment.depth() < tol) continue;
             finalNewSpaces.add(finalFragment);
             logger.trace("Adding potential final fragment: {}", finalFragment);
         }
     }

      // Add the valid, maximal new spaces to the main list
         mesList.addAll(finalNewSpaces);

         // NOW perform the single, final maximality check on the entire list
         logger.trace("Performing final maximality check on {} total spaces.", mesList.size());
         // Remove redundant MES (those fully contained within another) - crucial step 
         List<MaximalEmptySpace> toRemove = new ArrayList<>();
         for (int i = 0; i < mesList.size(); i++) {
             for (int j = 0; j < mesList.size(); j++) {
                 if (i == j) continue;
                 if (mesList.get(j).contains(mesList.get(i))) {
                     toRemove.add(mesList.get(i));
                     logger.trace("Marking MES {} for removal as it is contained within MES {}", mesList.get(i), mesList.get(j));
                     break; 
                 }
             }
         }
         mesList.removeAll(toRemove);

        logger.trace("MES list size after update: {}", mesList.size());
    }

    // Helper method (to be added to MaximalRectanglesAlgorithm class)
    /**
     * Subtracts an obstacle MES from a space MES, returning a list of non-overlapping fragments
     * that make up the original space minus the obstacle. Assumes the two inputs intersect.
     */
    private List<MaximalEmptySpace> subtractObstacle(MaximalEmptySpace space, MaximalEmptySpace obstacle) {
        List<MaximalEmptySpace> fragments = new ArrayList<>();
        double tol = 1e-6;

        // Calculate intersection box [ix1, iy1, iz1] to [ix2, iy2, iz2]
        double ix1 = Math.max(space.x, obstacle.x);
        double iy1 = Math.max(space.y, obstacle.y);
        double iz1 = Math.max(space.z, obstacle.z);
        double ix2 = Math.min(space.x + space.width(), obstacle.x + obstacle.width());
        double iy2 = Math.min(space.y + space.height(), obstacle.y + obstacle.height());
        double iz2 = Math.min(space.z + space.depth(), obstacle.z + obstacle.depth());

        // Check for non-intersection (adjusting for tolerance)
        if (ix1 >= ix2 - tol || iy1 >= iy2 - tol || iz1 >= iz2 - tol) {
            // No intersection or negligible intersection
            return List.of(space); 
        }

        // Slice 'space' into up to 6 fragments around the intersection box

        // Fragment 1: Left slice
        if (ix1 > space.x + tol) {
            fragments.add(new MaximalEmptySpace(space.x, space.y, space.z, ix1 - space.x, space.height(), space.depth()));
        }
        // Fragment 2: Right slice
        if (ix2 < space.x + space.width() - tol) {
            fragments.add(new MaximalEmptySpace(ix2, space.y, space.z, space.x + space.width() - ix2, space.height(), space.depth()));
        }
        // Fragment 3: Bottom slice
        if (iy1 > space.y + tol) {
            // Use full width and depth of original space for this slice
            fragments.add(new MaximalEmptySpace(space.x, space.y, space.z, space.width(), iy1 - space.y, space.depth()));
        }
        // Fragment 4: Top slice
        if (iy2 < space.y + space.height() - tol) {
            // Use full width and depth of original space
            fragments.add(new MaximalEmptySpace(space.x, iy2, space.z, space.width(), space.y + space.height() - iy2, space.depth()));
        }
        // Fragment 5: Back slice
        if (iz1 > space.z + tol) {
            // Use full width and height of original space
            fragments.add(new MaximalEmptySpace(space.x, space.y, space.z, space.width(), space.height(), iz1 - space.z));
        }
        // Fragment 6: Front slice
        if (iz2 < space.z + space.depth() - tol) {
            // Use full width and height of original space
            fragments.add(new MaximalEmptySpace(space.x, space.y, iz2, space.width(), space.height(), space.z + space.depth() - iz2));
        }

        // Filter out zero-volume fragments
        fragments.removeIf(f -> f.width() < tol || f.height() < tol || f.depth() < tol);

        return fragments;
    }
 
 } // End of MaximalRectanglesAlgorithm class
