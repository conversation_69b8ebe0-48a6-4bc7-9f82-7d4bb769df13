package com.packprime.innovademo.dto;

import com.packprime.innovademo.algorithm.VerticalOrientationConstraint;
import java.util.List;

public class PackingRequest {

  private BinDto bin;
  private List<PieceDto> pieces;
  private String algorithmType = "Default"; // Added for algorithm selection
  private AlgorithmSettingsDto algorithmSettings; // Added for general constraints/settings

  // Getters and Setters
  public BinDto getBin() {
    return bin;
  }

  public void setBin(BinDto bin) {
    this.bin = bin;
  }

  public List<PieceDto> getPieces() {
    return pieces;
  }

  public void setPieces(List<PieceDto> pieces) {
    this.pieces = pieces;
  }

  public String getAlgorithmType() {
    return algorithmType;
  }

  public void setAlgorithmType(String algorithmType) {
    this.algorithmType = algorithmType;
  }

  public AlgorithmSettingsDto getAlgorithmSettings() {
    return algorithmSettings;
  }

  public void setAlgorithmSettings(AlgorithmSettingsDto algorithmSettings) {
    this.algorithmSettings = algorithmSettings;
  }

  // Inner DTO for Bin details
  public static class BinDto {
    private double width;
    private double height;
    private double depth;
    private double maxWeight; // Added maxWeight constraint
    private Double overhangX; // Added overhang constraint (use wrapper for optionality)
    private Double overhangY; // Added overhang constraint (use wrapper for optionality)

    // Getters and Setters
    public double getWidth() {
      return width;
    }

    public void setWidth(double width) {
      this.width = width;
    }

    public double getHeight() {
      return height;
    }

    public void setHeight(double height) {
      this.height = height;
    }

    public double getDepth() {
      return depth;
    }

    public void setDepth(double depth) {
      this.depth = depth;
    }

    public double getMaxWeight() {
      return maxWeight;
    }

    public void setMaxWeight(double maxWeight) {
      this.maxWeight = maxWeight;
    }

    public Double getOverhangX() {
      return overhangX;
    }

    public void setOverhangX(Double overhangX) {
      this.overhangX = overhangX;
    }

    public Double getOverhangY() {
      return overhangY;
    }

    public void setOverhangY(Double overhangY) {
      this.overhangY = overhangY;
    }
  }

  // Inner DTO for Algorithm Settings/Restrictions
  public static class AlgorithmSettingsDto {
    private Boolean allowMixedColumns; // Example setting
    private Boolean allowMixedLayers; // Example setting
    private Double targetPercentage; // Target utilization percentage (e.g., 90.0 for 90%)
    private VerticalOrientationConstraint verticalConstraint = VerticalOrientationConstraint.HEIGHT_ONLY; // Default to HEIGHT_ONLY

    // Add other relevant algorithm settings here

    // Getters and Setters
    public Boolean getAllowMixedColumns() {
      return allowMixedColumns;
    }

    public void setAllowMixedColumns(Boolean allowMixedColumns) {
      this.allowMixedColumns = allowMixedColumns;
    }

    public Boolean getAllowMixedLayers() {
      return allowMixedLayers;
    }

    public void setAllowMixedLayers(Boolean allowMixedLayers) {
      this.allowMixedLayers = allowMixedLayers;
    }

    public Double getTargetPercentage() {
      return targetPercentage;
    }

    public void setTargetPercentage(Double targetPercentage) {
      this.targetPercentage = targetPercentage;
    }
    
    public VerticalOrientationConstraint getVerticalConstraint() {
      return verticalConstraint;
    }
    
    public void setVerticalConstraint(VerticalOrientationConstraint verticalConstraint) {
      this.verticalConstraint = verticalConstraint;
    }
  }

  // Inner DTO for Piece details
  public static class PieceDto {
    private String id; // Use String to match JSON flexibility
    private double width;
    private double height;
    private double depth;
    private double value; // Optional
    private double weight; // Added weight constraint
    private Boolean allowRotationX; // Use Boolean for optionality in JSON
    private Boolean allowRotationY;
    private Boolean allowRotationZ;
    private Integer stackingLimit; // Use Integer for optionality
    private Integer loadPriority;
    private Integer minQuantity;
    private Integer maxQuantity;
    private String shapeType; // "Cuboid" or "Cylinder"
    private Double diameter; // For cylinder shape

    // Getters and Setters
    public String getId() {
      return id;
    }

    public void setId(String id) {
      this.id = id;
    }

    public double getWidth() {
      return width;
    }

    public void setWidth(double width) {
      this.width = width;
    }

    public double getHeight() {
      return height;
    }

    public void setHeight(double height) {
      this.height = height;
    }

    public double getDepth() {
      return depth;
    }

    public void setDepth(double depth) {
      this.depth = depth;
    }

    public double getValue() {
      return value;
    }

    public void setValue(double value) {
      this.value = value;
    }

    public double getWeight() {
      return weight;
    }

    public void setWeight(double weight) {
      this.weight = weight;
    }

    public Boolean getAllowRotationX() {
      return allowRotationX;
    }

    public void setAllowRotationX(Boolean allowRotationX) {
      this.allowRotationX = allowRotationX;
    }

    public Boolean getAllowRotationY() {
      return allowRotationY;
    }

    public void setAllowRotationY(Boolean allowRotationY) {
      this.allowRotationY = allowRotationY;
    }

    public Boolean getAllowRotationZ() {
      return allowRotationZ;
    }

    public void setAllowRotationZ(Boolean allowRotationZ) {
      this.allowRotationZ = allowRotationZ;
    }

    public Integer getStackingLimit() {
      return stackingLimit;
    }

    public void setStackingLimit(Integer stackingLimit) {
      this.stackingLimit = stackingLimit;
    }

    public Integer getLoadPriority() {
      return loadPriority;
    }

    public void setLoadPriority(Integer loadPriority) {
      this.loadPriority = loadPriority;
    }

    public Integer getMinQuantity() {
      return minQuantity;
    }

    public void setMinQuantity(Integer minQuantity) {
      this.minQuantity = minQuantity;
    }

    public Integer getMaxQuantity() {
      return maxQuantity;
    }

    public void setMaxQuantity(Integer maxQuantity) {
      this.maxQuantity = maxQuantity;
    }

    public String getShapeType() {
      return shapeType;
    }

    public void setShapeType(String shapeType) {
      this.shapeType = shapeType;
    }

    public Double getDiameter() {
      return diameter;
    }

    public void setDiameter(Double diameter) {
      this.diameter = diameter;
    }
  }
}
