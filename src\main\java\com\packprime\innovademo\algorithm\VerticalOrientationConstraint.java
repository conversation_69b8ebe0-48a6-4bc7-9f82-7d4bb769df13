package com.packprime.innovademo.algorithm;

/**
 * Enum representing vertical orientation constraints for piece placement.
 * This defines which dimensions of a piece can be oriented vertically during packing.
 */
public enum VerticalOrientationConstraint {
    HEIGHT_ONLY,       // Only height can be vertical (default)
    WIDTH_ONLY,        // Only width can be vertical
    LENGTH_ONLY,       // Only length can be vertical
    HEIGHT_OR_WIDTH,   // Either height or width can be vertical
    HEIGHT_OR_LENGTH,  // Either height or length can be vertical
    WIDTH_OR_LENGTH,   // Either width or length can be vertical
    ANY                // Any dimension can be vertical
}
