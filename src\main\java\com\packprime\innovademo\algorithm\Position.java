package com.packprime.innovademo.algorithm;

import java.util.Objects;

public class Position {
  private double x;
  private double y;
  private double z;

  public Position(double x, double y, double z) {
    this.x = x;
    this.y = y;
    this.z = z;
  }

  // Getters
  public double getX() {
    return x;
  }

  public double getY() {
    return y;
  }

  public double getZ() {
    return z;
  }

  // Setters (optional, if needed)
  public void setX(double x) {
    this.x = x;
  }

  public void setY(double y) {
    this.y = y;
  }

  public void setZ(double z) {
    this.z = z;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    Position position = (Position) o;
    // Use a small tolerance for double comparison
    double epsilon = 1e-6;
    return Math.abs(position.x - x) < epsilon
        && Math.abs(position.y - y) < epsilon
        && Math.abs(position.z - z) < epsilon;
  }

  @Override
  public int hashCode() {
    // Note: hashCode for doubles can be tricky. This is a basic implementation.
    return Objects.hash(x, y, z);
  }

  @Override
  public String toString() {
    return "Position{" + "x=" + x + ", y=" + y + ", z=" + z + '}';
  }
}
