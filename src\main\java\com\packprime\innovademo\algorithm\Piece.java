package com.packprime.innovademo.algorithm;

public class Piece {
  private String id; // Corresponds to externalId or a generated ID
  private double width;
  private double height;
  private double depth;
  private double value;
  private double volume;
  private Position position; // Null if not placed
  private int orientation; // 0-5, representing different rotations
  private boolean isPlaced;

  // Store original dimensions for reset and orientation calculations
  private final double originalWidth;
  private final double originalHeight;
  private final double originalDepth;

  // Constraints (immutable for this instance)
  private final double weight;
  private final boolean allowRotationX;
  private final boolean allowRotationY;
  private final boolean allowRotationZ;
  private final int stackingLimit;
  private final int loadPriority;
  private final int minQuantity; // Note: Algorithm needs to handle quantity logic
  private final int maxQuantity; // Note: Algorithm needs to handle quantity logic

  // State related to stacking (mutable)
  private int piecesStackedOnTopCount; // How many pieces are currently placed directly on this one

  public Piece(
      String id,
      double width,
      double height,
      double depth,
      double value,
      double weight,
      boolean allowRotationX,
      boolean allowRotationY,
      boolean allowRotationZ,
      int stackingLimit,
      int loadPriority,
      int minQuantity,
      int maxQuantity) {
    this.id = id;
    this.originalWidth = width;
    this.originalHeight = height;
    this.originalDepth = depth;
    this.width = width;
    this.height = height;
    this.depth = depth;
    this.value = value;
    this.volume = width * height * depth;
    this.position = null;
    this.orientation = 0;
    this.isPlaced = false;

    // Initialize constraints
    this.weight = weight;
    this.allowRotationX = allowRotationX;
    this.allowRotationY = allowRotationY;
    this.allowRotationZ = allowRotationZ;
    this.stackingLimit = stackingLimit;
    this.loadPriority = loadPriority;
    this.minQuantity = minQuantity;
    this.maxQuantity = maxQuantity;

    // Initialize mutable state
    this.piecesStackedOnTopCount = 0;
  }

  /** Resets the piece to its initial state (unplaced, original dimensions, stack count). */
  public void reset() {
    this.position = null;
    this.orientation = 0;
    this.isPlaced = false;
    // Reset dimensions based on orientation 0
    this.width = this.originalWidth;
    this.height = this.originalHeight;
    this.depth = this.originalDepth;
    this.piecesStackedOnTopCount = 0; // Reset stack count
  }

  /**
   * Updates the piece's current dimensions based on the given orientation. This should be called
   * when the orientation changes.
   *
   * @param orientation The new orientation (0-5).
   */
  public void setDimensionsForOrientation(int orientation) {
    Dimensions dims = getDimensionsForOrientation(orientation);
    this.width = dims.w;
    this.height = dims.h;
    this.depth = dims.d;
    this.orientation = orientation; // Keep track of the current orientation
  }

  /**
   * Calculates the dimensions of the piece for a given orientation without modifying the piece's
   * state.
   *
   * @param orientation The orientation to check (0-5).
   * @return A Dimensions object with width, height, and depth for that orientation.
   */
  public Dimensions getDimensionsForOrientation(int orientation) {
    // Note: This method assumes the orientation is allowed.
    // Call isOrientationAllowed() first if needed.
    return switch (orientation) {
      // Rotation interpretations:
      // 0: W, H, D (Original)
      // 1: H, W, D (Rotate Z 90deg)
      // 2: W, D, H (Rotate Y 90deg)
      // 3: D, W, H (Rotate Y 90deg, then Z 90deg)
      // 4: H, D, W (Rotate X 90deg)
      // 5: D, H, W (Rotate X 90deg, then Z 90deg)
      case 1 -> new Dimensions(originalHeight, originalWidth, originalDepth); // H, W, D
      case 2 -> new Dimensions(originalWidth, originalDepth, originalHeight); // W, D, H
      case 3 -> new Dimensions(originalDepth, originalWidth, originalHeight); // D, W, H
      case 4 -> new Dimensions(originalHeight, originalDepth, originalWidth); // H, D, W
      case 5 -> new Dimensions(originalDepth, originalHeight, originalWidth); // D, H, W
      default -> new Dimensions(originalWidth, originalHeight, originalDepth); // 0: W, H, D
    };
  }

  /**
   * Checks if a given orientation is allowed based on the piece's constraints.
   *
   * @param orientation The orientation index (0-5).
   * @return true if allowed, false otherwise.
   */
  public boolean isOrientationAllowed(int orientation) {
    return switch (orientation) {
      case 0 -> true; // Original orientation is always allowed
      case 1 -> allowRotationZ; // H, W, D - Requires Z rotation
      case 2 -> allowRotationY; // W, D, H - Requires Y rotation
      case 3 -> allowRotationY && allowRotationZ; // D, W, H - Requires Y and Z rotation
      case 4 -> allowRotationX; // H, D, W - Requires X rotation
      case 5 ->
          allowRotationX
              && allowRotationZ; // D, H, W - Requires X and Z rotation (or X then Y depending on
      // convention)
      // Assuming Z rotation follows X rotation here based on common patterns.
      // If a different convention is needed, adjust logic.
      default -> false; // Invalid orientation index
    };
  }

  // --- Getters ---
  public String getId() {
    return id;
  }

  public double getWidth() {
    return width;
  }

  public double getHeight() {
    return height;
  }

  public double getDepth() {
    return depth;
  }

  public double getValue() {
    return value;
  }

  public double getVolume() {
    return volume;
  }

  public Position getPosition() {
    return position;
  }

  public int getOrientation() {
    return orientation;
  }

  public boolean isPlaced() {
    return isPlaced;
  }

  public double getOriginalWidth() {
    return originalWidth;
  }

  public double getOriginalHeight() {
    return originalHeight;
  }

  public double getOriginalDepth() {
    return originalDepth;
  }

  // Constraint Getters
  public double getWeight() {
    return weight;
  }

  public boolean isAllowRotationX() {
    return allowRotationX;
  }

  public boolean isAllowRotationY() {
    return allowRotationY;
  }

  public boolean isAllowRotationZ() {
    return allowRotationZ;
  }

  public int getStackingLimit() {
    return stackingLimit;
  }

  public int getLoadPriority() {
    return loadPriority;
  }

  public int getMinQuantity() {
    return minQuantity;
  }

  public int getMaxQuantity() {
    return maxQuantity;
  }

  // --- Setters ---
  public void setPosition(Position position) {
    this.position = position;
  }

  public void setPlaced(boolean placed) {
    isPlaced = placed;
  }

  // setOrientation is handled via setDimensionsForOrientation

  // --- Stacking State Modifiers ---

  /** Increments the count of pieces stacked directly on top of this piece. */
  public void incrementPiecesStackedOnTopCount() {
    this.piecesStackedOnTopCount++;
  }

  // --- Stacking State Getters ---

  /**
   * Gets the current number of pieces stacked directly on top of this piece.
   *
   * @return The count of pieces stacked on top.
   */
  public int getPiecesStackedOnTopCount() {
    return piecesStackedOnTopCount;
  }

  // Helper class for returning dimensions
  public static class Dimensions {
    public final double w, h, d;

    public Dimensions(double w, double h, double d) {
      this.w = w;
      this.h = h;
      this.d = d;
    }
  }

  @Override
  public String toString() {
    return "Piece{"
        + "id='"
        + id
        + '\''
        + ", width="
        + width
        + ", height="
        + height
        + ", depth="
        + depth
        + ", volume="
        + volume
        + ", position="
        + position
        + ", orientation="
        + orientation
        + ", isPlaced="
        + isPlaced
        + ", weight="
        + weight // Added constraints to toString
        + ", allowRotationX="
        + allowRotationX
        + ", allowRotationY="
        + allowRotationY
        + ", allowRotationZ="
        + allowRotationZ
        + ", stackingLimit="
        + stackingLimit
        + ", loadPriority="
        + loadPriority
        + ", minQuantity="
        + minQuantity
        + ", maxQuantity="
        + maxQuantity
        + '}';
  }
}
