"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { RuleTemplate, PackingRule } from '@/types/rule-types'
import { ruleEditorService } from '@/types/rule-editor-service'
import TemplateGallery from '@/components/rule-editor/template-gallery'
import FormEditor from '@/components/rule-editor/form-editor'
import JsonEditor from '@/components/rule-editor/json-editor'
import TestRunner from '@/components/rule-editor/test-runner'

export default function RuleEditorPage() {
  const [activeTab, setActiveTab] = useState('templates')
  const [templates, setTemplates] = useState<RuleTemplate[]>([])
  const [userRules, setUserRules] = useState<RuleTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<RuleTemplate | null>(null)
  const [currentRule, setCurrentRule] = useState<PackingRule | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Load templates and user rules on initial render
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      setError(null)
      try {
        // Load built-in templates
        const builtInTemplates = await ruleEditorService.getBuiltInTemplates()
        setTemplates(builtInTemplates)
        
        // Load user rules
        const userRulesList = await ruleEditorService.getUserRules()
        setUserRules(userRulesList)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error'
        setError(`Failed to load templates: ${errorMessage}`)
      } finally {
        setLoading(false)
      }
    }
    
    loadData()
  }, [])

  // Handle template selection
  const handleTemplateSelect = async (template: RuleTemplate) => {
    setLoading(true)
    setError(null)
    try {
      let ruleContent: PackingRule
      
      if (template.source === 'builtin') {
        ruleContent = await ruleEditorService.getBuiltInTemplateContent(template.id)
      } else {
        ruleContent = await ruleEditorService.getUserRuleContent(template.id)
      }
      
      setSelectedTemplate(template)
      setCurrentRule(ruleContent)
      setActiveTab('form')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(`Failed to load template content: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // Handle form changes
  const handleFormChange = (updatedRule: PackingRule) => {
    setCurrentRule(updatedRule)
  }

  // Handle JSON changes
  const handleJsonChange = (updatedRule: PackingRule) => {
    setCurrentRule(updatedRule)
  }

  // Save the current rule
  const handleSaveRule = async () => {
    if (!currentRule) return
    
    setLoading(true)
    setError(null)
    try {
      // Validate the rule first
      const validationResult = await ruleEditorService.validateRule(currentRule)
      if (!validationResult.valid) {
        setError(`Rule validation failed: ${validationResult.errors?.join(', ')}`)
        return
      }
      
      // Save the rule
      const savedRule = await ruleEditorService.saveUserRule(
        currentRule.name, 
        currentRule
      )
      
      // Update user rules list
      setUserRules(prev => [...prev, savedRule])
      
      // Set the saved rule as the selected template
      setSelectedTemplate(savedRule)
      
      // Show success message
      alert('Rule saved successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(`Failed to save rule: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  // Create a new rule
  const handleNewRule = () => {
    const newRule: PackingRule = {
      name: 'New Rule',
      description: 'A new packing rule',
      algorithmType: 'FirstFitDecreasingVolume',
      bin: {
        width: 1000,
        height: 1000,
        depth: 1000,
        maxWeight: 100
      },
      boxTypes: [
        {
          id: 'box1',
          width: 200,
          height: 200,
          depth: 200,
          weight: 5,
          quantity: 1,
          shapeType: 'Cuboid'
        }
      ],
      algorithmSettings: {
        allowRotation: true
      }
    }
    
    setSelectedTemplate(null)
    setCurrentRule(newRule)
    setActiveTab('form')
  }

  // Test the current rule
  const handleTestRule = () => {
    if (!currentRule) return
    setActiveTab('test')
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Rule Editor</h1>
        <Button variant="outline" onClick={() => router.push('/')}>
          Back to Home
        </Button>
      </div>
      
      <Separator className="my-4" />
      
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Rule Editor</CardTitle>
            <CardDescription>
              Create, edit, and test packing algorithm rules
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="templates">Templates</TabsTrigger>
                <TabsTrigger value="form" disabled={!currentRule}>Form Editor</TabsTrigger>
                <TabsTrigger value="json" disabled={!currentRule}>JSON Editor</TabsTrigger>
                <TabsTrigger value="test" disabled={!currentRule}>Test</TabsTrigger>
              </TabsList>
              
              <TabsContent value="templates">
                <TemplateGallery 
                  builtInTemplates={templates}
                  userRules={userRules}
                  onSelectTemplate={handleTemplateSelect}
                  onNewRule={handleNewRule}
                  loading={loading}
                />
              </TabsContent>
              
              <TabsContent value="form">
                {currentRule && (
                  <FormEditor 
                    rule={currentRule}
                    onChange={handleFormChange}
                    onSave={handleSaveRule}
                    onTest={handleTestRule}
                    loading={loading}
                  />
                )}
              </TabsContent>
              
              <TabsContent value="json">
                {currentRule && (
                  <JsonEditor 
                    rule={currentRule}
                    onChange={handleJsonChange}
                    onSave={handleSaveRule}
                    onTest={handleTestRule}
                    loading={loading}
                  />
                )}
              </TabsContent>
              
              <TabsContent value="test">
                {currentRule && (
                  <TestRunner 
                    rule={currentRule}
                    loading={loading}
                  />
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
