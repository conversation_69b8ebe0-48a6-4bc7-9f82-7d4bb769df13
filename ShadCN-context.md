# ShadCN UI Components Tracking

This file tracks all the ShadCN components used in the packing algorithm visualization frontend. It serves as a reference to check if we need to install a component from scratch with the CLI tool or if it's already available.

## Components Used

### Layout Components
- [ ] Card
  - [ ] CardHeader
  - [ ] CardContent
  - [ ] CardFooter
- [ ] Separator
- [ ] Tabs
  - [ ] TabsList
  - [ ] TabsTrigger
  - [ ] TabsContent

### Form Components
- [ ] Form
  - [ ] FormField
  - [ ] FormItem
  - [ ] FormLabel
  - [ ] FormControl
  - [ ] FormDescription
  - [ ] FormMessage
- [ ] Input
- [ ] Button
- [ ] Select
  - [ ] SelectTrigger
  - [ ] SelectValue
  - [ ] SelectContent
  - [ ] SelectItem
- [ ] Switch
- [ ] Label

### Data Display Components
- [ ] Table
  - [ ] TableHeader
  - [ ] TableBody
  - [ ] TableFooter
  - [ ] TableHead
  - [ ] TableRow
  - [ ] TableCell
  - [ ] TableCaption
- [ ] Alert
  - [ ] AlertTitle
  - [ ] AlertDescription
- [ ] Badge

### Overlay Components
- [ ] Dialog
  - [ ] DialogTrigger
  - [ ] DialogContent
  - [ ] DialogHeader
  - [ ] DialogTitle
  - [ ] DialogDescription
  - [ ] DialogFooter
- [ ] Tooltip
  - [ ] TooltipTrigger
  - [ ] TooltipContent

### Theme Components
- [ ] ThemeProvider
- [ ] ModeToggle

## Installation Status

When a component is installed using the ShadCN CLI, check the corresponding box and add the installation date.

Example:
- [x] Button (Installed: 2025-05-06)
- [x] Card (Installed: 2025-05-06)
- [x] Form (Installed: 2025-05-06)
- [x] Input (Installed: 2025-05-06)
- [x] Table (Installed: 2025-05-06)
- [x] Dialog (Installed: 2025-05-06)
- [x] Separator (Installed: 2025-05-06)
- [x] Alert (Installed: 2025-05-06)
- [x] Tooltip (Installed: 2025-05-06)
