package com.packprime.innovademo.dto;

// Represents a type of box with quantity and dimensions from industry datasets
public class BoxType {
    private String id;
    private int quantity;
    private double width;
    private double height;
    private double depth;
    private double weight; // Assuming weight might be relevant later
    private AllowedVerticalOrientations allowedVerticalOrientations;
    private String shapeType; // "Cuboid" or "Cylinder"
    private Double diameter; // For cylinder shape

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public int getQuantity() { return quantity; }
    public void setQuantity(int quantity) { this.quantity = quantity; }
    public double getWidth() { return width; }
    public void setWidth(double width) { this.width = width; }
    public double getHeight() { return height; }
    public void setHeight(double height) { this.height = height; }
    public double getDepth() { return depth; }
    public void setDepth(double depth) { this.depth = depth; }
    public double getWeight() { return weight; }
    public void setWeight(double weight) { this.weight = weight; }
    public AllowedVerticalOrientations getAllowedVerticalOrientations() { return allowedVerticalOrientations; }
    public void setAllowedVerticalOrientations(AllowedVerticalOrientations allowedVerticalOrientations) { this.allowedVerticalOrientations = allowedVerticalOrientations; }
    public String getShapeType() { return shapeType; }
    public void setShapeType(String shapeType) { this.shapeType = shapeType; }
    public Double getDiameter() { return diameter; }
    public void setDiameter(Double diameter) { this.diameter = diameter; }

    @Override
    public String toString() {
        return "BoxType{" +
               "id='" + id + '\'' +
               ", quantity=" + quantity +
               ", width=" + width +
               ", height=" + height +
               ", depth=" + depth +
               ", weight=" + weight +
               ", allowedVerticalOrientations=" + allowedVerticalOrientations +
               ", shapeType='" + shapeType + '\'' +
               ", diameter=" + diameter +
               '}';
    }
}
