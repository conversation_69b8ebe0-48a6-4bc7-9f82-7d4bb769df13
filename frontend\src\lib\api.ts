/**
 * API Service for communicating with the packing algorithm backend
 */

import { 
  PackingRequest, 
  PackingResponse, 
  IndustryPackingRequest,
  PackingConfiguration
} from '../types/frontend-types';

// Base URL for API endpoints - using relative path for proxy through Next.js
const API_BASE_URL = '/api';

/**
 * Performs a packing calculation using the standard packing endpoint
 * @param request The packing request data
 * @returns Promise with the packing response
 */
export async function performPacking(request: PackingRequest): Promise<PackingResponse> {
  try {
    // Log the request payload to see what's being sent
    console.log('Packing request payload:', JSON.stringify(request, null, 2));
    
    // Add a more visible alert for debugging
    alert('API call to /packing/pack with shape type: ' + 
          request.pieces.map(p => p.shapeType || 'unknown').join(', '));
    
    const response = await fetch(`${API_BASE_URL}/packing/pack`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.statusMessages?.[0] || 
        `API error: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();
    // Log the response to see what's coming back
    console.log('Packing response:', JSON.stringify(result, null, 2));
    
    // Add a more visible alert for debugging
    alert('API response received with pieces: ' + 
          (result.pieces ? result.pieces.length : 'none') + 
          '\nFirst piece shape type: ' + 
          (result.pieces && result.pieces.length > 0 ? result.pieces[0].shapeType : 'unknown'));
    
    return result;
  } catch (error) {
    console.error('Error performing packing:', error);
    throw error;
  }
}

/**
 * Performs an industry packing calculation
 * @param request The industry packing request data
 * @returns Promise with the packing response
 */
export async function performIndustryPacking(request: IndustryPackingRequest): Promise<PackingResponse> {
  try {
    // Log the industry packing request payload
    console.log('Industry packing request payload:', JSON.stringify(request, null, 2));
    
    const response = await fetch(`${API_BASE_URL}/packing/pack-industry`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.statusMessages?.[0] || 
        `API error: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();
    // Log the industry packing response
    console.log('Industry packing response:', JSON.stringify(result, null, 2));
    return result;
  } catch (error) {
    console.error('Error performing industry packing:', error);
    throw error;
  }
}

/**
 * Fetches the list of available industry data files from the backend
 * @returns Promise with the list of industry data filenames
 */
export async function getIndustryDataFiles(): Promise<string[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/list-industry-data`);
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching industry data files:', error);
    throw error;
  }
}

/**
 * Fetches a specific industry data file content
 * @param filename The name of the industry data file to fetch
 * @returns Promise with the industry data content
 */
export async function getIndustryDataFileContent(filename: string): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/industry-data/${filename}`);
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error fetching industry data file ${filename}:`, error);
    throw error;
  }
}
