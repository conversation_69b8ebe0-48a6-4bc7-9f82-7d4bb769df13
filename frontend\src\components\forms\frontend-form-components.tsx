/**
 * Form Components for Packing Algorithm Frontend
 * These components handle user input for secondary and primary packs
 */

import React, { useState } from 'react';
import { BinDto, PieceDto, PackingFormState } from '../../types/frontend-types';
import SecondaryPackForm from './secondary-pack-form';
import PrimaryPacksForm from './primary-pack-form';

// Props for the SecondaryPackForm component
interface SecondaryPackFormProps {
  value: BinDto;
  onChange: (value: BinDto) => void;
  className?: string;
}

// Props for the PrimaryPacksForm component
interface PrimaryPacksFormProps {
  value: Array<PieceDto & { name: string; quantity: number }>;
  onChange: (value: Array<PieceDto & { name: string; quantity: number }>) => void;
  className?: string;
}

// Props for the PackingForm component
interface PackingFormProps {
  value: PackingFormState;
  onChange: (value: PackingFormState) => void;
  onSubmit: () => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * Main packing form component that combines secondary and primary pack forms
 */
export const PackingForm: React.FC<PackingFormProps> = ({
  value,
  onChange,
  onSubmit,
  isLoading = false,
  className,
}) => {
  // Handle secondary pack changes
  const handleSecondaryPackChange = (secondaryPack: BinDto) => {
    onChange({
      ...value,
      secondaryPack,
    });
  };

  // Handle primary packs changes
  const handlePrimaryPacksChange = (primaryPacks: Array<PieceDto & { name: string; quantity: number }>) => {
    onChange({
      ...value,
      primaryPacks,
    });
  };

  // Handle algorithm type change
  const handleAlgorithmTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange({
      ...value,
      algorithmType: e.target.value,
    });
  };

  return (
    <div className={`space-y-6 ${className || ''}`}>
      <SecondaryPackForm
        value={value.secondaryPack}
        onChange={handleSecondaryPackChange}
      />
      
      <PrimaryPacksForm
        value={value.primaryPacks}
        onChange={handlePrimaryPacksChange}
      />
      
      <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h2 className="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">Algorithm Settings</h2>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="algorithm-type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Algorithm Type
            </label>
            <select
              id="algorithm-type"
              name="algorithmType"
              value={value.algorithmType}
              onChange={handleAlgorithmTypeChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="Default">Default Algorithm</option>
              <option value="FirstFitDecreasingVolume">First Fit Decreasing Volume</option>
              <option value="MaximalRectangles">Maximal Rectangles</option>
            </select>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button
          onClick={onSubmit}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Processing...' : 'Run Packing Algorithm'}
        </button>
      </div>
    </div>
  );
};

export default PackingForm;
