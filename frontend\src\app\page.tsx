"use client"

/**
 * Main Application Component for Packing Algorithm Frontend
 * This component integrates all the pieces of the application
 */

import React, { useState, useEffect } from 'react';
import { PackingForm } from '../components/forms/frontend-form-components';
import PackingVisualization from '../components/three/frontend-visualization';
import ResultsTable from '../components/tables/frontend-results-table';
import { 
  PackingFormState, 
  PackingResponse, 
  PackingRequest,
  PackingConfiguration
} from '../types/frontend-types';
import { 
  performPacking,
  saveConfiguration,
  loadConfiguration,
  getAllConfigurations,
  exportConfigurationToFile,
  importConfigurationFromFile
} from '../types/frontend-api-service';

export default function Home() {
  // State for form data
  const [formState, setFormState] = useState<PackingFormState>({
    secondaryPack: {
      width: 800,
      height: 600,
      depth: 400,
      maxWeight: 25,
    },
    primaryPacks: [],
    algorithmType: 'Default',
  });

  // State for packing results
  const [packingResult, setPackingResult] = useState<PackingResponse | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);

  // State for save/load dialog
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isLoadDialogOpen, setIsLoadDialogOpen] = useState(false);
  const [configurationName, setConfigurationName] = useState('');
  const [savedConfigurations, setSavedConfigurations] = useState<PackingConfiguration[]>([]);

  // Load saved configurations on mount
  useEffect(() => {
    const configs = getAllConfigurations();
    setSavedConfigurations(configs);
  }, []);

  // Handle form changes
  const handleFormChange = (newFormState: PackingFormState) => {
    setFormState(newFormState);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      setError(undefined);

      // Convert form state to API request
      const request: PackingRequest = {
        bin: formState.secondaryPack,
        pieces: formState.primaryPacks.flatMap(pack => {
          // Create multiple pieces based on quantity
          return Array.from({ length: pack.quantity }).map((_, index) => ({
            id: `${pack.id}_instance_${index + 1}`,
            width: pack.width,
            height: pack.height,
            depth: pack.depth,
            weight: pack.weight,
            allowRotationX: pack.allowRotationX,
            allowRotationY: pack.allowRotationY,
            allowRotationZ: pack.allowRotationZ,
            shapeType: pack.shapeType || 'Cuboid',
            diameter: pack.diameter,
          }));
        }),
        algorithmType: formState.algorithmType,
        algorithmSettings: formState.algorithmSettings,
      };

      // Call API
      const result = await performPacking(request);
      setPackingResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error performing packing:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle save configuration
  const handleSaveConfiguration = () => {
    if (!configurationName.trim()) {
      alert('Please enter a configuration name');
      return;
    }

    const config: PackingConfiguration = {
      name: configurationName,
      timestamp: Date.now(),
      secondaryPack: formState.secondaryPack,
      primaryPacks: formState.primaryPacks,
      algorithmType: formState.algorithmType,
      algorithmSettings: formState.algorithmSettings,
    };

    saveConfiguration(config);
    setIsSaveDialogOpen(false);
    setConfigurationName('');

    // Refresh saved configurations
    setSavedConfigurations(getAllConfigurations());
  };

  // Handle load configuration
  const handleLoadConfiguration = (name: string) => {
    const config = loadConfiguration(name);
    if (config) {
      setFormState({
        secondaryPack: config.secondaryPack,
        primaryPacks: config.primaryPacks,
        algorithmType: config.algorithmType,
        algorithmSettings: config.algorithmSettings,
      });
      setIsLoadDialogOpen(false);
    }
  };

  // Create a mapping of primary pack IDs to names for the results table
  const primaryPackNames: Record<string, string> = {};
  formState.primaryPacks.forEach(pack => {
    primaryPackNames[pack.id] = pack.name;
  });

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-6">
            <h1 className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Packing Visualizer
            </h1>
            <a href="/visual-tests" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
              Visual Tests
            </a>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setIsSaveDialogOpen(true)}
              className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
            >
              Save
            </button>
            <button
              onClick={() => setIsLoadDialogOpen(true)}
              className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
            >
              Load
            </button>
            <button
              onClick={() => {
                document.documentElement.classList.toggle('dark');
              }}
              className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 hidden dark:block"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 block dark:hidden"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                />
              </svg>
            </button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error message */}
        {error && (
          <div className="mb-6 bg-red-100 dark:bg-red-900/20 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-400">Error</h3>
            <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Split view layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left column: Forms */}
          <div className="space-y-6">
            <PackingForm
              value={formState}
              onChange={handleFormChange}
              onSubmit={handleSubmit}
              isLoading={isLoading}
            />
          </div>

          {/* Right column: Visualization */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <h2 className="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">3D Visualization</h2>
              <div className="h-[500px]">
                <PackingVisualization
                  secondaryPack={formState.secondaryPack}
                  packingResult={packingResult}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results table (full width) */}
        <div className="mt-8">
          <ResultsTable
            packingResult={packingResult}
            primaryPackNames={primaryPackNames}
          />
        </div>
      </main>

      {/* Save dialog */}
      {isSaveDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Save Configuration
            </h3>
            <div>
              <label htmlFor="configName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Configuration Name
              </label>
              <input
                type="text"
                id="configName"
                value={configurationName}
                onChange={(e) => setConfigurationName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter configuration name"
              />
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setIsSaveDialogOpen(false);
                  setConfigurationName('');
                }}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveConfiguration}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Load dialog */}
      {isLoadDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Load Configuration
            </h3>
            {savedConfigurations.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">No saved configurations found.</p>
            ) : (
              <div className="max-h-60 overflow-y-auto">
                <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                  {savedConfigurations.map((config) => (
                    <li key={config.name} className="py-2">
                      <button
                        onClick={() => handleLoadConfiguration(config.name)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                      >
                        <div className="font-medium text-gray-900 dark:text-white">{config.name}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(config.timestamp).toLocaleString()}
                        </div>
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setIsLoadDialogOpen(false)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
