package com.packprime.innovademo.dto;

import java.util.List;

public class PackingResponse {

  private List<PlacedPieceDto> placedPieces;
  private List<String> unplacedPieceIds; // IDs of pieces that couldn't be placed
  private double totalWeight; // Total weight of placed pieces
  private double volumeUtilization; // Percentage of bin volume used by placed pieces
  private String algorithmUsed; // Added: Algorithm used for this result
  private List<String> statusMessages; // Added: Messages about constraints, errors, etc.
  private List<PackingRequest.PieceDto> pieces; // Original pieces for visualization

  // Constructor
  public PackingResponse(
      List<PlacedPieceDto> placedPieces,
      List<String> unplacedPieceIds,
      double totalWeight,
      double volumeUtilization,
      String algorithmUsed, // Added
      List<String> statusMessages, // Added
      List<PackingRequest.PieceDto> pieces // Added
      ) {
    this.placedPieces = placedPieces;
    this.unplacedPieceIds = unplacedPieceIds;
    this.totalWeight = totalWeight;
    this.volumeUtilization = volumeUtilization;
    this.algorithmUsed = algorithmUsed; // Added
    this.statusMessages = statusMessages; // Added
    this.pieces = pieces; // Added
  }

  // Getters
  public List<PlacedPieceDto> getPlacedPieces() {
    return placedPieces;
  }

  public List<String> getUnplacedPieceIds() {
    return unplacedPieceIds;
  }

  public double getTotalWeight() {
    return totalWeight;
  }

  public double getVolumeUtilization() {
    return volumeUtilization;
  }

  public String getAlgorithmUsed() { // Added getter
    return algorithmUsed;
  }

  public List<String> getStatusMessages() { // Added getter
    return statusMessages;
  }

  public List<PackingRequest.PieceDto> getPieces() { // Added getter
    return pieces;
  }

  // Setters (optional)
  public void setPlacedPieces(List<PlacedPieceDto> placedPieces) {
    this.placedPieces = placedPieces;
  }

  public void setUnplacedPieceIds(List<String> unplacedPieceIds) {
    this.unplacedPieceIds = unplacedPieceIds;
  }

  public void setTotalWeight(double totalWeight) {
    this.totalWeight = totalWeight;
  }

  public void setVolumeUtilization(double volumeUtilization) {
    this.volumeUtilization = volumeUtilization;
  }

  public void setPieces(List<PackingRequest.PieceDto> pieces) { // Added setter
    this.pieces = pieces;
  }

  // Inner DTO for representing a placed piece in the response
  public static class PlacedPieceDto {
    private String id;
    private double originalWidth; // Send original dimensions for reference
    private double originalHeight;
    private double originalDepth;
    private double placedWidth; // Dimensions in the placed orientation
    private double placedHeight;
    private double placedDepth;
    private double weight; // Added weight
    private PositionDto position;
    private int orientation;

    // Constructor
    public PlacedPieceDto(
        String id,
        double originalWidth,
        double originalHeight,
        double originalDepth,
        double placedWidth,
        double placedHeight,
        double placedDepth,
        double weight, // Added weight
        PositionDto position,
        int orientation) {
      this.id = id;
      this.originalWidth = originalWidth;
      this.originalHeight = originalHeight;
      this.originalDepth = originalDepth;
      this.placedWidth = placedWidth;
      this.placedHeight = placedHeight;
      this.placedDepth = placedDepth;
      this.weight = weight; // Added weight
      this.position = position;
      this.orientation = orientation;
    }

    // Getters
    public String getId() {
      return id;
    }

    public double getOriginalWidth() {
      return originalWidth;
    }

    public double getOriginalHeight() {
      return originalHeight;
    }

    public double getOriginalDepth() {
      return originalDepth;
    }

    public double getPlacedWidth() {
      return placedWidth;
    }

    public double getPlacedHeight() {
      return placedHeight;
    }

    public double getPlacedDepth() {
      return placedDepth;
    }

    public double getWeight() {
      return weight;
    }

    public PositionDto getPosition() {
      return position;
    }

    public int getOrientation() {
      return orientation;
    }
  }

  // Inner DTO for Position
  public static class PositionDto {
    private double x;
    private double y;
    private double z;

    public PositionDto(double x, double y, double z) {
      this.x = x;
      this.y = y;
      this.z = z;
    }

    // Getters
    public double getX() {
      return x;
    }

    public double getY() {
      return y;
    }

    public double getZ() {
      return z;
    }
  }
}
