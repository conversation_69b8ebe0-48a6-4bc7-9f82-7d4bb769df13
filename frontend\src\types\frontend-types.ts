/**
 * TypeScript type definitions for the packing algorithm frontend
 * These types match the Java backend DTOs
 */

// Secondary Pack (Container)
export interface BinDto {
  width: number;
  height: number;
  depth: number;
  maxWeight: number;
  overhangX?: number;
  overhangY?: number;
}

// Primary Pack (Item to be packed)
export interface PieceDto {
  id: string;
  width: number;
  height: number;
  depth: number;
  weight: number;
  value?: number;
  allowRotationX?: boolean;
  allowRotationY?: boolean;
  allowRotationZ?: boolean;
  stackingLimit?: number;
  loadPriority?: number;
  minQuantity?: number;
  maxQuantity?: number;
  shapeType?: 'Cuboid' | 'Cylinder';
  diameter?: number;
}

// Algorithm Settings
export interface AlgorithmSettingsDto {
  allowMixedColumns?: boolean;
  allowMixedLayers?: boolean;
  targetPercentage?: number;
}

// Packing Request (sent to API)
export interface PackingRequest {
  bin: BinDto;
  pieces: PieceDto[];
  algorithmType: string;
  algorithmSettings?: AlgorithmSettingsDto;
}

// Position of a placed piece
export interface PositionDto {
  x: number;
  y: number;
  z: number;
}

// Placed piece in the response
export interface PlacedPieceDto {
  id: string;
  originalWidth: number;
  originalHeight: number;
  originalDepth: number;
  placedWidth: number;
  placedHeight: number;
  placedDepth: number;
  weight: number;
  position: PositionDto;
  orientation: number;
}

// Packing Response (received from API)
export interface PackingResponse {
  placedPieces: PlacedPieceDto[];
  unplacedPieceIds: string[];
  totalWeight: number;
  volumeUtilization: number;
  algorithmUsed: string;
  statusMessages: string[];
  pieces: PieceDto[];
  bin: BinDto;
}

// Box Type for industry packing
export interface BoxType {
  id: string;
  width: number;
  height: number;
  depth: number;
  weight: number;
  quantity: number;
  allowedVerticalOrientations?: {
    width: boolean;
    height: boolean;
    depth: boolean;
  };
  shapeType?: 'Cuboid' | 'Cylinder';
  diameter?: number;
}

// Industry Packing Request
export interface IndustryPackingRequest {
  bin: BinDto;
  boxTypes: BoxType[];
}

// Configuration for saving/loading
export interface PackingConfiguration {
  name: string;
  timestamp: number;
  secondaryPack: BinDto;
  primaryPacks: Array<PieceDto & { name: string; quantity: number }>;
  algorithmType: string;
  algorithmSettings?: AlgorithmSettingsDto;
}

// Form state for the UI
export interface PackingFormState {
  secondaryPack: BinDto;
  primaryPacks: Array<PieceDto & { name: string; quantity: number }>;
  algorithmType: string;
  algorithmSettings?: AlgorithmSettingsDto;
  selectedIndustryData?: string;
}

// Results state for the UI
export interface PackingResultsState {
  isLoading: boolean;
  error?: string;
  response?: PackingResponse;
}

// Color mapping for visualization
export interface ColorMapping {
  [key: string]: string; // Maps piece ID to color
}

// Industry data file metadata
export interface IndustryDataFile {
  filename: string;
  displayName: string;
  description?: string;
}

// Industry data state
export interface IndustryDataState {
  availableFiles: IndustryDataFile[];
  isLoading: boolean;
  error?: string;
  selectedFile?: string;
  fileContent?: any;
}
