# ShadCN/UI Components Used in InnovaDemo Project

This file tracks all ShadCN/UI components used in the project to help manage dependencies and ensure consistency.

## Components Currently Used

### Layout Components
- Card, CardHeader, CardContent, CardTitle, CardFooter

### Form Components
- Form, FormField, FormItem, FormLabel, FormControl
- Input
- Select, SelectContent, SelectItem, SelectTrigger, SelectValue

### Button Components
- Button

### Data Display Components
- Table, TableHeader, TableBody, TableRow, TableCell, TableHead

### Feedback Components
- Alert, AlertDescription

### Other Components
- Separator
- <PERSON><PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger
- Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger
- Skeleton

## Components Used in Industry Data Selector
- Card, CardHeader, CardContent, CardTitle
- Select, SelectContent, SelectItem, SelectTrigger, SelectValue
- But<PERSON>
- <PERSON><PERSON>, AlertDescription

## Components Used in Visual Tests Page
- Card, CardHeader, CardContent, CardTitle, CardFooter, CardDescription
- Button
- Tabs, TabsContent, TabsList, TabsTrigger
- Separator
- Skeleton
- Alert, AlertDescription, AlertTitle
