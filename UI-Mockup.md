# UI Mockup for Packing Visualization Application

## Main Page Layout

```
+----------------------------------------------------------------------+
|                          PACKING VISUALIZER                [🌙/☀️]    |
+----------------------------------------------------------------------+
|                                  |                                   |
|  +-------------------------+     |     +-------------------------+   |
|  | Secondary Pack          |     |     |                         |   |
|  +-------------------------+     |     |                         |   |
|  | Length (mm): [      ]   |     |     |                         |   |
|  | Width (mm):  [      ]   |     |     |                         |   |
|  | Height (mm): [      ]   |     |     |       3D VISUALIZATION  |   |
|  | Max Weight (kg): [    ] |     |     |                         |   |
|  +-------------------------+     |     |                         |   |
|                                  |     |                         |   |
|  +-------------------------+     |     |                         |   |
|  | Primary Packs           |     |     |                         |   |
|  +-------------------------+     |     |                         |   |
|  | [Table of Primary Packs]|     |     +-------------------------+   |
|  |                         |     |                                   |
|  | + Add Primary Pack      |     |     +-------------------------+   |
|  +-------------------------+     |     | Packing Statistics      |   |
|                                  |     +-------------------------+   |
|  +-------------------------+     |     | Volume Utilization: 78% |   |
|  | [Pack] [Save] [Load]    |     |     | Total Weight: 12.5 kg   |   |
|  +-------------------------+     |     | Items Packed: 15/20     |   |
|                                  |     +-------------------------+   |
+----------------------------------------------------------------------+
|                                                                      |
| +------------------------------------------------------------------+ |
| | Results                                                          | |
| +------------------------------------------------------------------+ |
| | [Table of Packing Results]                                       | |
| |                                                                  | |
| +------------------------------------------------------------------+ |
|                                                                      |
+----------------------------------------------------------------------+
```

## Component Details

### Secondary Pack Form

```
+-------------------------+
| Secondary Pack          |
+-------------------------+
| Length (mm): [  800  ] |
| Width (mm):  [  600  ] |
| Height (mm): [  400  ] |
| Max Weight (kg): [ 25 ] |
+-------------------------+
```

### Primary Packs Table

```
+---------------------------------------------------------------+
| Primary Packs                                       [+ Add]   |
+---------------------------------------------------------------+
| Name    | L×W×H (mm)     | Weight (kg) | Quantity | Actions  |
+---------+----------------+-------------+----------+----------+
| Box A   | 200×150×100    | 2.5         | 5        | [✏️] [🗑️] |
| Box B   | 300×200×150    | 4.0         | 3        | [✏️] [🗑️] |
| Box C   | 150×100×100    | 1.5         | 8        | [✏️] [🗑️] |
+---------+----------------+-------------+----------+----------+
```

### Add/Edit Primary Pack Dialog

```
+---------------------------------------+
| Add Primary Pack                  [X] |
+---------------------------------------+
| Name:        [                     ] |
| Length (mm): [                     ] |
| Width (mm):  [                     ] |
| Height (mm): [                     ] |
| Weight (kg): [                     ] |
| Quantity:    [                     ] |
|                                       |
| [Cancel]                    [Save]    |
+---------------------------------------+
```

### 3D Visualization

```
+----------------------------------+
|                                  |
|                                  |
|                                  |
|          [Secondary Pack]        |
|          (Semi-transparent)      |
|                                  |
|          [Primary Packs]         |
|          (Solid colors)          |
|                                  |
|                                  |
|                                  |
+----------------------------------+
| [🔄 Reset View] [⚙️ Settings]     |
+----------------------------------+
```

### Results Table

```
+------------------------------------------------------------------------------+
| Results                                                                      |
+------------------------------------------------------------------------------+
| Name  | Dimensions (mm) | Weight | Position (X,Y,Z) | Orientation | Status   |
+-------+----------------+--------+-----------------+-------------+----------+
| Box A | 200×150×100    | 2.5 kg | 0, 0, 0         | Original    | ✅ Placed |
| Box A | 200×150×100    | 2.5 kg | 200, 0, 0       | Original    | ✅ Placed |
| Box B | 300×200×150    | 4.0 kg | 0, 0, 100       | Rotated     | ✅ Placed |
| Box C | 150×100×100    | 1.5 kg | 300, 0, 0       | Original    | ✅ Placed |
| Box B | 300×200×150    | 4.0 kg | -               | -           | ❌ Unplaced|
+-------+----------------+--------+-----------------+-------------+----------+
```

### Save/Load Configuration Dialog

```
+---------------------------------------+
| Save Configuration                [X] |
+---------------------------------------+
| Name: [MyPackingConfig              ] |
|                                       |
| [Cancel]                    [Save]    |
+---------------------------------------+
```

```
+---------------------------------------+
| Load Configuration                [X] |
+---------------------------------------+
| Select Configuration:                 |
| [▼ MyPackingConfig                  ] |
|                                       |
| [Cancel]                    [Load]    |
+---------------------------------------+
```

## Color Scheme

### Dark Mode (Default)
- Background: Dark (#121212)
- Card Background: Slightly lighter dark (#1E1E1E)
- Text: White (#FFFFFF)
- Primary: Blue (#1E40AF)
- Accent: Light Blue (#3B82F6)
- Success: Green (#10B981)
- Warning: Yellow (#F59E0B)
- Error: Red (#EF4444)

### Light Mode
- Background: White (#FFFFFF)
- Card Background: Light Gray (#F3F4F6)
- Text: Dark (#121212)
- Primary: Blue (#1E40AF)
- Accent: Light Blue (#3B82F6)
- Success: Green (#10B981)
- Warning: Yellow (#F59E0B)
- Error: Red (#EF4444)

## 3D Visualization Color Coding

- Secondary Pack: Semi-transparent blue (#1E40AF with 40% opacity)
- Primary Packs: Different solid colors for each type:
  - Type A: Red (#EF4444)
  - Type B: Green (#10B981)
  - Type C: Yellow (#F59E0B)
  - Type D: Purple (#8B5CF6)
  - Type E: Pink (#EC4899)
  - (Additional colors as needed)

## Mobile View Considerations

On mobile devices, the layout will stack vertically:

1. Secondary Pack form
2. Primary Packs table
3. Action buttons
4. 3D Visualization
5. Packing Statistics
6. Results Table

## Responsive Breakpoints

- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px
