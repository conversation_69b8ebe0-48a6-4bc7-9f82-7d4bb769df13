package com.packprime.innovademo.repository;

import com.packprime.innovademo.model.BinDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/** Spring Data JPA repository for BinDefinition entities. */
@Repository
public interface BinDefinitionRepository extends JpaRepository<BinDefinition, Long> {
  // JpaRepository provides standard CRUD operations.
  // Custom query methods can be added here if needed.
}
