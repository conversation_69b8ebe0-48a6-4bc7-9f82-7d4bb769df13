{"name": "Simple FFDV Test", "description": "A simple test case for the FirstFitDecreasingVolumeAlgorithm", "bin": {"width": 100, "height": 100, "depth": 100, "maxWeight": 1000, "overhangX": 0, "overhangY": 0}, "boxTypes": [{"id": "large_box", "width": 60, "height": 60, "depth": 60, "weight": 200, "quantity": 1, "shapeType": "Cuboid"}, {"id": "medium_box", "width": 40, "height": 40, "depth": 40, "weight": 100, "quantity": 2, "shapeType": "Cuboid"}, {"id": "small_box", "width": 20, "height": 20, "depth": 20, "weight": 50, "quantity": 4, "shapeType": "Cuboid"}], "algorithmType": "FirstFitDecreasingVolume", "algorithmSettings": {"verticalConstraint": "ANY"}}