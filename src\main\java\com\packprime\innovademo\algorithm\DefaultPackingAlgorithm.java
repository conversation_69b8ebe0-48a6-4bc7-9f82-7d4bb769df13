package com.packprime.innovademo.algorithm;

import com.packprime.innovademo.dto.PackingRequest; // Import for AlgorithmSettingsDto
import com.packprime.innovademo.model.BinDefinition; // Import for Bin constraints
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map; // Added for grouping
import java.util.Optional;
import java.util.stream.Collectors; // Added for grouping
// Added for stream operations
import org.slf4j.Logger; // Added logger
import org.slf4j.LoggerFactory; // Added logger
import org.springframework.stereotype.Component; // Add Component annotation for potential injection

/**
 * The default packing algorithm implementation, based on finding the best placement according to a
 * heuristic at each step.
 */
@Component("Default") // Give it a name for potential lookup
public class DefaultPackingAlgorithm implements PackingAlgorithm {

  private static final Logger logger =
      LoggerFactory.getLogger(DefaultPackingAlgorithm.class); // Added logger

  // Simple record to hold placement details (moved from PackingService)
  private record Placement(
      Piece piece, Position corner, int orientation, int pieceIndex, double score) {}

  /**
   * Attempts to pack the given pieces into the provided bin using the default heuristic. The state
   * of the bin and the placed pieces within the input list will be modified.
   *
   * @param bin The bin to pack into. Its state (placed pieces, corners, weight) will be modified.
   * @param pieces The list of pieces to pack.
   * @param binDefinition The definition of the bin, containing constraints like maxWeight,
   *     overhang.
   * @param settings Additional algorithm-specific settings or general constraints. Can be null.
   * @return A PackingResult object containing the list of placed pieces, unplaced pieces, and any
   *     status messages.
   */
  @Override
  public PackingResult pack(
      Bin bin,
      List<Piece> pieces,
      BinDefinition binDefinition,
      PackingRequest.AlgorithmSettingsDto settings) {
    logger.info(
        "Starting DefaultPackingAlgorithm. Pieces: {}, Bin: {}x{}x{}, MaxWeight: {}",
        pieces.size(),
        bin.getWidth(),
        bin.getHeight(),
        bin.getDepth(),
        binDefinition.getMaxWeight());
    bin.reset(); // Ensure bin is empty
    List<Piece> allPieces = new ArrayList<>(pieces); // Keep original list for reference
    allPieces.forEach(Piece::reset); // Ensure all pieces are reset
    List<Piece> remainingPieces = new ArrayList<>(allPieces); // Mutable list for processing
    List<String> statusMessages = new ArrayList<>(); // To collect messages

    // Initial sort: Prioritize by loadPriority (ascending - lower number is higher priority),
    // then by volume (descending)
    remainingPieces.sort(
        Comparator.comparingInt(Piece::getLoadPriority) // Ascending priority (0 is highest)
            .thenComparing(Comparator.comparingDouble(Piece::getVolume).reversed()));
    logger.debug("Sorted {} pieces by priority/volume.", remainingPieces.size());

    // Target Percentage Handling
    double targetUtilization = -1.0; // Default to no target
    if (settings != null && settings.getTargetPercentage() != null) {
      double requestedTarget = settings.getTargetPercentage();
      if (requestedTarget > 0 && requestedTarget <= 100) {
        targetUtilization = requestedTarget;
        logger.info("Target volume utilization set to: {}%", targetUtilization);
      } else {
        logger.warn("Invalid targetPercentage ({}) provided. Ignoring target.", requestedTarget);
        statusMessages.add("Warning: Invalid targetPercentage (" + requestedTarget + ") ignored.");
      }
    }

    int iteration = 0; // Add iteration counter for logging
    double currentVolumeUtilization = 0.0; // Track current utilization

    // --- Main Packing Loop ---
    while (!remainingPieces.isEmpty()) {
      iteration++;
      logger.trace("Packing iteration {}, remaining pieces: {}", iteration, remainingPieces.size());
      // Pass constraints down to placement finding logic
      Optional<Placement> bestPlacementOpt =
          findBestPlacement(bin, remainingPieces, binDefinition, settings, statusMessages);

      if (bestPlacementOpt.isPresent()) {
        Placement bestPlacement = bestPlacementOpt.get();
        logger.debug(
            "Iteration {}: Placing piece {} at {} (Orient: {}, Score: {:.2f})",
            iteration,
            bestPlacement.piece().getId(),
            bestPlacement.corner(),
            bestPlacement.orientation(),
            bestPlacement.score());
        bin.placePiece(bestPlacement.piece(), bestPlacement.corner(), bestPlacement.orientation());
        remainingPieces.remove(bestPlacement.pieceIndex());

        // Check target utilization after placing the piece
        if (targetUtilization > 0 && bin.getVolume() > 1e-9) {
          double totalPlacedVolume =
              bin.getPlacedPieces().stream().mapToDouble(Piece::getVolume).sum();
          currentVolumeUtilization = (totalPlacedVolume / bin.getVolume()) * 100.0;
          logger.trace("Current volume utilization: {:.2f}%", currentVolumeUtilization);
          if (currentVolumeUtilization >= targetUtilization) {
            logger.info(
                "Target volume utilization ({:.2f}%) reached or exceeded ({:.2f}%). Stopping"
                    + " packing.",
                targetUtilization, currentVolumeUtilization);
            statusMessages.add(
                String.format(
                    "Packing stopped: Target volume utilization (%.2f%%) reached (%.2f%%).",
                    targetUtilization, currentVolumeUtilization));
            break; // Stop packing
          }
        }
      } else {
        // No more pieces can be placed
        logger.debug("Iteration {}: No suitable placement found for remaining pieces.", iteration);
        if (!remainingPieces.isEmpty()) {
          // Don't add a generic failure message here, as minQuantity check might move pieces later
          logger.warn(
              "Could not find placement for {} pieces: {}",
              remainingPieces.size(),
              remainingPieces.stream().map(Piece::getId).collect(Collectors.joining(", ")));
        }
        break;
      }
    }
    // --- End Main Packing Loop ---

    // --- Post-packing checks and adjustments ---
    List<Piece> initiallyPlacedPieces = new ArrayList<>(bin.getPlacedPieces());
    List<Piece> finallyUnplacedPieces = new ArrayList<>(remainingPieces);

    // Group placed pieces by their original ID
    Map<String, List<Piece>> placedGroups =
        initiallyPlacedPieces.stream().collect(Collectors.groupingBy(Piece::getId));

    // Map original pieces by ID for easy lookup of constraints
    Map<String, Piece> originalPieceMap =
        allPieces.stream()
            .collect(
                Collectors.toMap(
                    Piece::getId,
                    p -> p,
                    (p1, p2) -> {
                      // Use the one with potentially stricter constraints if duplicates exist?
                      // Or just the first one. For now, first one.
                      if (p1.getMinQuantity() != p2.getMinQuantity()
                          || p1.getMaxQuantity() != p2.getMaxQuantity()) {
                        logger.warn(
                            "Duplicate piece ID '{}' found with different min/max quantities."
                                + " Using constraints from the first encountered.",
                            p1.getId());
                      }
                      return p1;
                    }));

    logger.debug("Performing post-packing min/max quantity checks and enforcement...");
    List<Piece> piecesToRemove = new ArrayList<>(); // Pieces to move from placed to unplaced

    // Check Min Quantity Violations
    originalPieceMap.forEach(
        (id, originalPiece) -> {
          if (originalPiece.getMinQuantity() > 0) {
            long count = placedGroups.getOrDefault(id, List.of()).size();
            if (count < originalPiece.getMinQuantity()) {
              String message =
                  String.format(
                      "Constraint Violation: Minimum quantity for piece %s not met (placed %d,"
                          + " required %d). Removing placed pieces.",
                      id, count, originalPiece.getMinQuantity());
              statusMessages.add(message);
              logger.warn(message);
              piecesToRemove.addAll(
                  placedGroups.getOrDefault(id, List.of())); // Mark all pieces of this type
            }
          }
        });

    // Check Max Quantity Violations (Sanity Check - should be prevented earlier)
    placedGroups.forEach(
        (id, placedList) -> {
          Piece originalPiece = originalPieceMap.get(id);
          if (originalPiece != null
              && originalPiece.getMaxQuantity() < Integer.MAX_VALUE
              && placedList.size() > originalPiece.getMaxQuantity()) {
            String message =
                String.format(
                    "Logic Error: Maximum quantity for piece %s exceeded (placed %d, max %d)."
                        + " This should have been prevented during placement.",
                    id, placedList.size(), originalPiece.getMaxQuantity());
            statusMessages.add(message);
            logger.error(message);
            // Decide on recovery: remove excess? For now, just log the error.
          }
        });

    // Perform the removal if any minQuantity violations occurred
    if (!piecesToRemove.isEmpty()) {
      logger.info("Removing {} pieces due to minimum quantity violations.", piecesToRemove.size());
      // Remove from the initially placed list
      boolean removed = initiallyPlacedPieces.removeAll(piecesToRemove);
      if (!removed) {
        logger.error("Failed to remove pieces marked for minQuantity violation!");
      }
      // Add to the finally unplaced list
      finallyUnplacedPieces.addAll(piecesToRemove);
      // Re-sort unplaced pieces? Optional, might be useful for clarity.
      finallyUnplacedPieces.sort(
          Comparator.comparingInt(Piece::getLoadPriority)
              .thenComparing(Comparator.comparingDouble(Piece::getVolume).reversed()));
    }

    logger.debug("Post-packing checks complete.");

    // Final assignment
    List<Piece> finalPlacedPieces = initiallyPlacedPieces;

    logger.info(
        "DefaultPackingAlgorithm finished. Placed: {}, Unplaced: {}, Messages: {}",
        finalPlacedPieces.size(),
        finallyUnplacedPieces.size(),
        statusMessages.size());
    return new PackingResult(finalPlacedPieces, finallyUnplacedPieces, statusMessages);
  }

  /**
   * Finds the best possible placement for any of the remaining pieces in the current bin state.
   *
   * @param bin The current state of the bin.
   * @param remainingPieces The list of pieces yet to be placed.
   * @param binDefinition Constraints related to the bin (maxWeight, overhang).
   * @param settings General algorithm settings.
   * @param statusMessages List to add messages to (e.g., constraint violations).
   * @return An Optional containing the best Placement found, or empty if no piece can be placed.
   */
  private Optional<Placement> findBestPlacement(
      Bin bin,
      List<Piece> remainingPieces,
      BinDefinition binDefinition,
      PackingRequest.AlgorithmSettingsDto settings,
      List<String> statusMessages) {
    Placement bestPlacement = null;
    double bestScore = Double.NEGATIVE_INFINITY;
    int checksPerformed = 0; // Counter for logging

    // Count currently placed pieces for maxQuantity check
    Map<String, Long> currentPlacedCounts =
        bin.getPlacedPieces().stream()
            .collect(Collectors.groupingBy(Piece::getId, Collectors.counting()));

    logger.trace(
        "Finding best placement among {} remaining pieces and {} corners.",
        remainingPieces.size(),
        bin.getCorners().size());

    for (int i = 0; i < remainingPieces.size(); i++) {
      Piece piece = remainingPieces.get(i);

      // --- Max Quantity Check ---
      long countAlreadyPlaced = currentPlacedCounts.getOrDefault(piece.getId(), 0L);
      if (piece.getMaxQuantity() < Integer.MAX_VALUE
          && countAlreadyPlaced >= piece.getMaxQuantity()) {
        logger.trace(
            "Skipping piece {}: Max quantity ({}) already placed ({}).",
            piece.getId(),
            piece.getMaxQuantity(),
            countAlreadyPlaced);
        continue; // Skip this piece entirely if its max quantity is already met
      }

      for (Position corner : bin.getCorners()) {
        for (int orientation = 0; orientation < 6; orientation++) {
          checksPerformed++;
          // 1. Check Piece Orientation Constraint
          if (!piece.isOrientationAllowed(orientation)) {
            // logger.trace("Skipping piece {} orientation {}: Not allowed by piece.",
            // piece.getId(), orientation); // Can be noisy
            continue; // Skip this orientation if not allowed by the piece itself
          }

          // 2. Check Placement Feasibility (Collision, Bounds, and other Constraints)
          List<String> checkMessages =
              new ArrayList<>(); // Use temporary list for this specific check
          boolean canPlace =
              bin.checkPlacement(
                  piece, corner, orientation, binDefinition, settings, checkMessages);

          if (canPlace) {
            logger.trace(
                "Placement check PASSED for piece {} at {} (Orient {})",
                piece.getId(),
                corner,
                orientation);
            Piece.Dimensions dims = piece.getDimensionsForOrientation(orientation);
            // This null check should ideally not be needed if isOrientationAllowed is robust
            if (dims == null) {
              String errorMsg =
                  "Error: Got null dimensions for allowed orientation "
                      + orientation
                      + " of piece "
                      + piece.getId();
              statusMessages.add(errorMsg);
              logger.error(errorMsg);
              continue; // Should not happen
            }

            // 3. Evaluate Heuristic Score if placement is valid
            double currentScore =
                evaluatePlacementHeuristic(
                    piece,
                    corner,
                    dims,
                    orientation,
                    bin,
                    binDefinition,
                    settings); // Pass constraints

            logger.trace(
                "Heuristic score for piece {} at {} (Orient {}): {:.2f}",
                piece.getId(),
                corner,
                orientation,
                currentScore);

            if (currentScore > bestScore) {
              logger.trace(
                  "New best score found: {:.2f} (Piece: {}, Corner: {}, Orient: {})",
                  currentScore,
                  piece.getId(),
                  corner,
                  orientation);
              bestScore = currentScore;
              bestPlacement = new Placement(piece, corner, orientation, i, currentScore);
            }
          } else {
            // Log why placement failed if needed (checkMessages might be noisy)
            // logger.trace("Placement check FAILED for piece {} at {} (Orient {}): {}",
            // piece.getId(), corner, orientation, String.join("; ", checkMessages));
          }
        }
      }
    }
    logger.trace(
        "Best placement search complete. Checks performed: {}. Best score: {:.2f}",
        checksPerformed,
        bestScore);
    return Optional.ofNullable(bestPlacement);
  }

  /**
   * Evaluates the quality of a potential placement based on a heuristic.
   *
   * @param piece The piece being considered.
   * @param corner The corner being considered.
   * @param dims The dimensions of the piece in the current orientation.
   * @param orientation The orientation being considered.
   * @param bin The bin state.
   * @param binDefinition Constraints for the bin.
   * @param settings General algorithm settings.
   * @return A score representing the quality of the placement (higher is better).
   */
  private double evaluatePlacementHeuristic(
      Piece piece,
      Position corner,
      Piece.Dimensions dims,
      int orientation,
      Bin bin,
      BinDefinition binDefinition, // Added
      PackingRequest.AlgorithmSettingsDto settings // Added
      ) {
    // --- Start with previous heuristic ---
    double score = piece.getVolume() * 1.0; // Base score on volume

    // Penalty for being further from origin (consider bin dimensions for normalization?)
    double positionPenalty = (corner.getX() + corner.getY() + corner.getZ()) * 0.5;
    score -= positionPenalty;

    // Bonus for placing with largest area face down (assuming Z is up)
    // This might need adjustment based on coordinate system interpretation
    if (dims.w * dims.d > dims.w * dims.h && dims.w * dims.d > dims.h * dims.d) {
      score += piece.getVolume() * 0.1; // Small bonus for flat placement
    }

    // Strong penalty for height (encourage packing lower first) - Assuming Y is height
    score -= corner.getY() * 1.0;
    // --- End of previous heuristic ---

    // --- Add Constraint/Preference Factors ---

    // Bonus for higher priority pieces (already handled by initial sort, but can reinforce here)
    // Lower priority number = higher actual priority. Give bonus for lower numbers.
    // Max priority could be 0, min could be large. Invert and scale?
    // Let's use a simple large bonus for priority 0, smaller for 1, etc.
    // score += (10 - piece.getLoadPriority()) * 10.0; // Example: Adjust multiplier/logic
    // Initial sort is likely sufficient for priority handling in this simple algorithm.

    // Penalize placing heavy items high up (assuming Y is height)
    if (piece.getWeight() > 0 && corner.getY() > 0) {
      score -= (piece.getWeight() * corner.getY()) * 0.05; // Adjust penalty factor as needed
    }

    // --- Heuristics based on Settings ---
    // Check if mixed layers are disallowed AND this piece is not on the floor
    if (settings != null
        && settings.getAllowMixedLayers() != null
        && !settings.getAllowMixedLayers()
        && corner.getY() > 0) {
      // Check if pieces directly below are of the same type (ID)
      boolean mixedLayerViolation = false;
      Box currentPieceBox = new Box(corner, dims);
      List<Piece> supporting = findSupportingPieces(currentPieceBox, bin.getPlacedPieces());
      if (!supporting.isEmpty()) { // Only check if actually supported
        for (Piece supportingPiece : supporting) {
          if (!supportingPiece.getId().equals(piece.getId())) {
            mixedLayerViolation = true;
            break;
          }
        }
      } else if (corner.getY() > 1e-6) {
        // If not on the floor (Y>0) and has no supporting pieces, it's floating - should have been
        // caught by checkPlacement
        logger.warn(
            "Piece {} at {} (Y>0) has no supporting pieces found by heuristic check.",
            piece.getId(),
            corner);
      }

      if (mixedLayerViolation) {
        score -= 1000.0; // Apply a significant penalty for violating mixed layer constraint
        logger.trace("Applying mixed layer penalty for piece {} at {}", piece.getId(), corner);
      }
    }

    // TODO: Implement penalty for violating 'allowMixedColumns' if settings.isAllowMixedColumns()
    // is false.

    return score;
  }

  // Helper method to find supporting pieces
  private List<Piece> findSupportingPieces(Box placedBox, List<Piece> allPlacedPieces) {
    List<Piece> supportingPieces = new ArrayList<>();
    final double EPSILON = 1e-6; // Use same epsilon as Bin

    for (Piece potentialSupporter : allPlacedPieces) {
      // Skip checking against itself if it somehow ended up in the list (shouldn't happen here)
      if (potentialSupporter.getPosition() == null) continue; // Should not happen for placed pieces

      Piece.Dimensions supporterDims =
          potentialSupporter.getDimensionsForOrientation(potentialSupporter.getOrientation());
      if (supporterDims == null) continue; // Should not happen
      Box supporterBox = new Box(potentialSupporter.getPosition(), supporterDims);

      // Check if the top face of supporterBox is directly below the bottom face of placedBox
      boolean isDirectlyBelow = Math.abs(supporterBox.maxY - placedBox.minY) < EPSILON;

      if (isDirectlyBelow) {
        // Check for overlap in the XZ plane (footprint overlap)
        boolean overlapXZ =
            (placedBox.maxX > supporterBox.minX + EPSILON
                    && placedBox.minX < supporterBox.maxX - EPSILON)
                && (placedBox.maxZ > supporterBox.minZ + EPSILON
                    && placedBox.minZ < supporterBox.maxZ - EPSILON);

        if (overlapXZ) {
          supportingPieces.add(potentialSupporter);
        }
      }
    }
    return supportingPieces;
  }

  // Helper class for Box definition
  private static class Box {
    final double minX, minY, minZ;
    final double maxX, maxY, maxZ;

    Box(Position corner, Piece.Dimensions dims) {
      this.minX = corner.getX();
      this.minY = corner.getY();
      this.minZ = corner.getZ();
      this.maxX = corner.getX() + dims.w;
      this.maxY = corner.getY() + dims.h;
      this.maxZ = corner.getZ() + dims.d;
    }
  }
}
