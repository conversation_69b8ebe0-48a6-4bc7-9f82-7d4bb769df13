import { RuleTemplate, PackingRule } from './rule-types';

/**
 * Service for managing rule templates and user rules.
 */
export const ruleEditorService = {
  /**
   * Gets a list of all built-in rule templates.
   * 
   * @returns Promise with list of rule templates
   */
  async getBuiltInTemplates(): Promise<RuleTemplate[]> {
    try {
      const response = await fetch('http://localhost:8080/api/rules/templates');
      if (!response.ok) {
        throw new Error(`Failed to fetch templates: ${response.status} ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching built-in templates:', error);
      throw error;
    }
  },

  /**
   * Gets a list of all user-created rule templates.
   * 
   * @returns Promise with list of user rule templates
   */
  async getUserRules(): Promise<RuleTemplate[]> {
    try {
      const response = await fetch('http://localhost:8080/api/rules/user');
      if (!response.ok) {
        throw new Error(`Failed to fetch user rules: ${response.status} ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching user rules:', error);
      throw error;
    }
  },

  /**
   * Gets the content of a built-in template by ID.
   * 
   * @param id Template ID
   * @returns Promise with template content
   */
  async getBuiltInTemplateContent(id: string): Promise<PackingRule> {
    try {
      const response = await fetch(`http://localhost:8080/api/rules/template/${id}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch template content: ${response.status} ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching built-in template content:', error);
      throw error;
    }
  },

  /**
   * Gets the content of a user rule by ID.
   * 
   * @param id Rule ID
   * @returns Promise with rule content
   */
  async getUserRuleContent(id: string): Promise<PackingRule> {
    try {
      const response = await fetch(`http://localhost:8080/api/rules/user/${id}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch user rule content: ${response.status} ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching user rule content:', error);
      throw error;
    }
  },

  /**
   * Saves a user rule.
   * 
   * @param name Rule name
   * @param content Rule content
   * @returns Promise with saved rule template
   */
  async saveUserRule(name: string, content: PackingRule): Promise<RuleTemplate> {
    try {
      const response = await fetch('http://localhost:8080/api/rules/user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, content }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to save rule: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error saving user rule:', error);
      throw error;
    }
  },

  /**
   * Deletes a user rule by ID.
   * 
   * @param id Rule ID
   * @returns Promise with success status
   */
  async deleteUserRule(id: string): Promise<boolean> {
    try {
      const response = await fetch(`http://localhost:8080/api/rules/user/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete rule: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Error deleting user rule:', error);
      throw error;
    }
  },

  /**
   * Validates a rule without saving it.
   * 
   * @param content Rule content to validate
   * @returns Promise with validation result
   */
  async validateRule(content: PackingRule): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const response = await fetch('http://localhost:8080/api/rules/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(content),
      });
      
      return await response.json();
    } catch (error) {
      console.error('Error validating rule:', error);
      throw error;
    }
  }
};
