package com.packprime.innovademo.dto;

// Used within BoxType to represent orientation constraints
public class AllowedVerticalOrientations {
    private boolean depth;
    private boolean width;
    private boolean height;

    // Getters and Setters
    public boolean isDepth() {
        return depth;
    }

    public void setDepth(boolean depth) {
        this.depth = depth;
    }

    public boolean isWidth() {
        return width;
    }

    public void setWidth(boolean width) {
        this.width = width;
    }

    public boolean isHeight() {
        return height;
    }

    public void setHeight(boolean height) {
        this.height = height;
    }

    @Override
    public String toString() {
        return "AllowedVerticalOrientations{" +
               "depth=" + depth +
               ", width=" + width +
               ", height=" + height +
               '}';
    }
}
