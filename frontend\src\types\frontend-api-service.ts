/**
 * API Service for communicating with the packing algorithm backend
 */

import { 
  PackingRequest, 
  PackingResponse, 
  IndustryPackingRequest,
  PackingConfiguration
} from './frontend-types';

// Base URL for API endpoints - adjust as needed for your environment
const API_BASE_URL = 'http://localhost:8080/api';

/**
 * Performs a packing calculation using the standard packing endpoint
 * @param request The packing request data
 * @returns Promise with the packing response
 */
export async function performPacking(request: PackingRequest): Promise<PackingResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/packing/pack`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.statusMessages?.[0] || 
        `API error: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error('Error performing packing:', error);
    throw error;
  }
}

/**
 * Performs an industry packing calculation
 * @param request The industry packing request data
 * @returns Promise with the packing response
 */
export async function performIndustryPacking(request: IndustryPackingRequest): Promise<PackingResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/packing/pack-industry`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.statusMessages?.[0] || 
        `API error: ${response.status} ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error('Error performing industry packing:', error);
    throw error;
  }
}

// Local storage key for saved configurations
const STORAGE_KEY = 'packingConfigurations';

/**
 * Saves a packing configuration to local storage
 * @param config The configuration to save
 */
export function saveConfiguration(config: PackingConfiguration): void {
  try {
    // Get existing configurations
    const existingConfigsStr = localStorage.getItem(STORAGE_KEY);
    const existingConfigs: PackingConfiguration[] = existingConfigsStr 
      ? JSON.parse(existingConfigsStr) 
      : [];
    
    // Check if configuration with same name exists
    const existingIndex = existingConfigs.findIndex(c => c.name === config.name);
    
    if (existingIndex >= 0) {
      // Update existing configuration
      existingConfigs[existingIndex] = config;
    } else {
      // Add new configuration
      existingConfigs.push(config);
    }
    
    // Save back to local storage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(existingConfigs));
  } catch (error) {
    console.error('Error saving configuration:', error);
    throw new Error('Failed to save configuration');
  }
}

/**
 * Loads a packing configuration from local storage
 * @param name The name of the configuration to load
 * @returns The loaded configuration or null if not found
 */
export function loadConfiguration(name: string): PackingConfiguration | null {
  try {
    const existingConfigsStr = localStorage.getItem(STORAGE_KEY);
    if (!existingConfigsStr) return null;
    
    const existingConfigs: PackingConfiguration[] = JSON.parse(existingConfigsStr);
    return existingConfigs.find(c => c.name === name) || null;
  } catch (error) {
    console.error('Error loading configuration:', error);
    throw new Error('Failed to load configuration');
  }
}

/**
 * Gets all saved configurations
 * @returns Array of saved configurations
 */
export function getAllConfigurations(): PackingConfiguration[] {
  try {
    const existingConfigsStr = localStorage.getItem(STORAGE_KEY);
    return existingConfigsStr ? JSON.parse(existingConfigsStr) : [];
  } catch (error) {
    console.error('Error getting configurations:', error);
    return [];
  }
}

/**
 * Deletes a configuration from local storage
 * @param name The name of the configuration to delete
 */
export function deleteConfiguration(name: string): void {
  try {
    const existingConfigsStr = localStorage.getItem(STORAGE_KEY);
    if (!existingConfigsStr) return;
    
    const existingConfigs: PackingConfiguration[] = JSON.parse(existingConfigsStr);
    const filteredConfigs = existingConfigs.filter(c => c.name !== name);
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredConfigs));
  } catch (error) {
    console.error('Error deleting configuration:', error);
    throw new Error('Failed to delete configuration');
  }
}

/**
 * Exports a configuration to a JSON file
 * @param config The configuration to export
 */
export function exportConfigurationToFile(config: PackingConfiguration): void {
  try {
    const dataStr = JSON.stringify(config, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const exportFileDefaultName = `${config.name}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  } catch (error) {
    console.error('Error exporting configuration:', error);
    throw new Error('Failed to export configuration');
  }
}

/**
 * Imports a configuration from a JSON file
 * @param file The JSON file to import
 * @returns Promise with the imported configuration
 */
export function importConfigurationFromFile(file: File): Promise<PackingConfiguration> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const config = JSON.parse(event.target?.result as string) as PackingConfiguration;
        resolve(config);
      } catch (error) {
        reject(new Error('Invalid configuration file'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsText(file);
  });
}
