"use client"

/**
 * Three.js Visualization Component for Packing Algorithm
 * This component renders a 3D visualization of the packing solution
 */

import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls, PerspectiveCamera, Grid } from '@react-three/drei';
import { Canvas, useThree, useFrame } from '@react-three/fiber';
import { BinDto, PlacedPieceDto, PackingResponse } from '../../types/frontend-types';

interface PackingVisualizationProps {
  secondaryPack: BinDto;
  packingResult?: PackingResponse;
  className?: string;
}

// Color palette for primary packs
const colorPalette = [
  '#EF4444', // Red
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#8B5CF6', // Purple
  '#EC4899', // Pink
  '#3B82F6', // Blue
  '#F97316', // Orange
  '#14B8A6', // Teal
  '#6366F1', // Indigo
  '#A855F7', // Violet
];

// Helper to generate a consistent color for a piece ID
const getPieceColor = (id: string): string => {
  // Extract the base ID without instance number
  const baseId = id.split('_instance_')[0];
  // Use hash function to get a consistent index
  const hash = baseId.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);
  const index = Math.abs(hash) % colorPalette.length;
  return colorPalette[index];
};

// Secondary Pack Component
const SecondaryPackMesh: React.FC<{ dimensions: BinDto }> = ({ dimensions }) => {
  return (
    <group name="secondaryPack">
      {/* Semi-transparent container */}
      <mesh position={[dimensions.width / 2, dimensions.height / 2, dimensions.depth / 2]}>
        <boxGeometry args={[dimensions.width, dimensions.height, dimensions.depth]} />
        <meshBasicMaterial color="#1E40AF" transparent opacity={0.2} />
      </mesh>
      
      {/* Wireframe edges */}
      <lineSegments position={[dimensions.width / 2, dimensions.height / 2, dimensions.depth / 2]}>
        <edgesGeometry args={[new THREE.BoxGeometry(dimensions.width, dimensions.height, dimensions.depth)]} />
        <lineBasicMaterial color="#1E40AF" />
      </lineSegments>
    </group>
  );
};

// Primary Pack Component
const PrimaryPackMesh: React.FC<{ piece: PlacedPieceDto; index: number }> = ({ piece, index }) => {
  const color = getPieceColor(piece.id);
  
  // Find the original piece data from the pieces array in packingResult
  const pieceIdBase = piece.id.split('_instance_')[0];
  
  // Check if this is a cylinder by looking at the piece ID pattern
  // Since we don't have direct access to the original piece data here,
  // we'll need to infer it from the piece ID or other properties
  const isCylinder = piece.id.includes('cylinder') || 
                    (piece.placedWidth === piece.placedHeight && 
                     piece.placedWidth !== piece.placedDepth);
  
  // Debug log for the first piece
  if (index === 0) {
    console.warn('Rendering piece:', piece.id, 'Is cylinder (inferred):', isCylinder, 'Orientation:', piece.orientation);
  }
  
  return (
    <group name={`primaryPack_${piece.id}_${index}`} 
           position={[
             piece.position.x + piece.placedWidth / 2,
             piece.position.y + piece.placedHeight / 2,
             piece.position.z + piece.placedDepth / 2
           ]}>
      {/* Render different geometry based on shape type */}
      {isCylinder ? (
        // Cylinder
        <>
          <mesh rotation={
            // Handle different orientations
            piece.orientation === 0 ? [Math.PI/2, 0, 0] :  // Horizontal along X-axis
            piece.orientation === 1 ? [0, Math.PI/2, 0] :  // Vertical along Z-axis
            [0, 0, Math.PI/2]                              // Horizontal along Y-axis
          }>
            <cylinderGeometry 
              args={[
                piece.placedWidth / 2, // top radius
                piece.placedWidth / 2, // bottom radius
                piece.placedDepth,     // height
                32                     // radial segments
              ]} 
            />
            <meshStandardMaterial color={color} metalness={0.1} roughness={0.5} />
          </mesh>
          
          {/* Wireframe for cylinder */}
          <lineSegments rotation={
            // Handle different orientations
            piece.orientation === 0 ? [Math.PI/2, 0, 0] :  // Horizontal along X-axis
            piece.orientation === 1 ? [0, Math.PI/2, 0] :  // Vertical along Z-axis
            [0, 0, Math.PI/2]                              // Horizontal along Y-axis
          }>
            <wireframeGeometry args={[
              new THREE.CylinderGeometry(
                piece.placedWidth / 2,
                piece.placedWidth / 2,
                piece.placedDepth,
                32
              )
            ]} />
            <lineBasicMaterial color="#000000" transparent opacity={0.5} />
          </lineSegments>
        </>
      ) : (
        // Cuboid (default)
        <>
          <mesh>
            <boxGeometry args={[piece.placedWidth, piece.placedHeight, piece.placedDepth]} />
            <meshStandardMaterial color={color} metalness={0.1} roughness={0.5} />
          </mesh>
          
          {/* Wireframe edges */}
          <lineSegments>
            <edgesGeometry args={[new THREE.BoxGeometry(piece.placedWidth, piece.placedHeight, piece.placedDepth)]} />
            <lineBasicMaterial color="#000000" transparent opacity={0.5} />
          </lineSegments>
        </>
      )}
    </group>
  );
};

// Scene setup component
const Scene: React.FC<{
  secondaryPack: BinDto;
  packingResult?: PackingResponse;
}> = ({ secondaryPack, packingResult }) => {
  const { camera, scene } = useThree();
  
  // Setup camera position based on secondary pack dimensions
  useEffect(() => {
    if (secondaryPack) {
      // Calculate the diagonal of the box for better camera positioning
      const diagonal = Math.sqrt(
        Math.pow(secondaryPack.width, 2) + 
        Math.pow(secondaryPack.height, 2) + 
        Math.pow(secondaryPack.depth, 2)
      );
      
      // Update camera far plane to ensure visibility of large containers
      camera.far = Math.max(10000, diagonal * 10);
      
      // Position camera at an isometric view relative to the box size
      const distance = diagonal * 1.2;
      camera.position.set(
        distance,
        distance * 0.8,
        distance
      );
      
      // Update target to center of the box
      camera.lookAt(
        secondaryPack.width / 2,
        secondaryPack.height / 2,
        secondaryPack.depth / 2
      );
      
      camera.updateProjectionMatrix();
    }
  }, [camera, secondaryPack]);
  
  const maxDimension = Math.max(
    secondaryPack.width,
    secondaryPack.height,
    secondaryPack.depth,
    1000 // Minimum size to ensure grid is visible for small containers
  );
  
  return (
    <>
      {/* Lights */}
      <ambientLight intensity={0.5} />
      <directionalLight position={[1000, 1000, 1000]} intensity={0.8} />
      <directionalLight position={[-1000, 500, -500]} intensity={0.3} />
      
      {/* Grid */}
      <Grid 
        infiniteGrid 
        cellSize={100}
        cellThickness={0.5}
        sectionSize={1000}
        sectionThickness={1}
        sectionColor="#2080ff"
        fadeDistance={maxDimension * 3}
      />
      
      {/* Axes helper */}
      <axesHelper args={[maxDimension]} />
      
      {/* Secondary pack */}
      <SecondaryPackMesh dimensions={secondaryPack} />
      
      {/* Primary packs */}
      {packingResult?.placedPieces?.map((piece, index) => (
        <PrimaryPackMesh key={`${piece.id}_${index}`} piece={piece} index={index} />
      ))}
    </>
  );
};

// Main visualization component
const PackingVisualization: React.FC<PackingVisualizationProps> = ({
  secondaryPack,
  packingResult,
  className,
}) => {
  const orbitControlsRef = useRef(null);
  
  // Calculate a good near and far plane based on container size
  const diagonal = Math.sqrt(
    Math.pow(secondaryPack.width, 2) + 
    Math.pow(secondaryPack.height, 2) + 
    Math.pow(secondaryPack.depth, 2)
  );
  
  const near = 0.1;
  const far = Math.max(10000, diagonal * 10);
  
  // Reset camera function
  const resetCamera = () => {
    // Reset is handled by the OrbitControls reset method
    // The camera will automatically be positioned correctly when the component re-renders
    if (orbitControlsRef.current) {
      // Force re-render to reset camera position
      window.dispatchEvent(new Event('resize'));
    }
  };

  return (
    <div className={`w-full h-full min-h-[400px] ${className || ''}`} style={{ position: 'relative' }}>
      <Canvas 
        shadows 
        dpr={[1, 2]} 
        gl={{ antialias: true }}
        camera={{ 
          fov: 50, 
          near: near,
          far: far,
          position: [diagonal, diagonal * 0.8, diagonal]
        }}
      >
        <color attach="background" args={["#121212"]} />
        
        <Scene 
          secondaryPack={secondaryPack} 
          packingResult={packingResult} 
        />
        
        <OrbitControls 
          ref={orbitControlsRef}
          target={[secondaryPack.width / 2, secondaryPack.height / 2, secondaryPack.depth / 2]}
          enableDamping
          dampingFactor={0.05}
          minDistance={10}
          maxDistance={diagonal * 5}
        />
      </Canvas>
      
      {/* UI overlay elements */}
      <div className="absolute bottom-2 left-2 z-10 flex gap-2">
        <button 
          className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
          onClick={resetCamera}
        >
          Reset View
        </button>
      </div>
    </div>
  );
};

export default PackingVisualization;
