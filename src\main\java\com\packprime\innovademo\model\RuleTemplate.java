package com.packprime.innovademo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Represents a rule template for packing algorithms.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleTemplate {
    private String id;
    private String name;
    private String description;
    private String algorithmType;
    private String source; // "builtin" or "user"
    private String filename;
    private String createdAt;
    private String lastModified;
    
    // Getters and setters explicitly defined to ensure compatibility
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getAlgorithmType() {
        return algorithmType;
    }
    
    public void setAlgorithmType(String algorithmType) {
        this.algorithmType = algorithmType;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getFilename() {
        return filename;
    }
    
    public void setFilename(String filename) {
        this.filename = filename;
    }
    
    public String getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
    
    public String getLastModified() {
        return lastModified;
    }
    
    public void setLastModified(String lastModified) {
        this.lastModified = lastModified;
    }
}
