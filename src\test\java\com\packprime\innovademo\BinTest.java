package com.packprime.innovademo;

import static org.junit.jupiter.api.Assertions.*;

import com.packprime.innovademo.algorithm.Bin;
import com.packprime.innovademo.algorithm.Piece;
import com.packprime.innovademo.algorithm.Position;
import com.packprime.innovademo.dto.PackingRequest;
import com.packprime.innovademo.model.BinDefinition;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BinTest {

  private Bin bin;
  private BinDefinition binDefinition;
  private PackingRequest.AlgorithmSettingsDto settings;
  private List<String> statusMessages;

  // Standard piece for testing
  private Piece testPiece;

  @BeforeEach
  void setUp() {
    // Standard Bin: 10x10x10, Max Weight 100, Overhang 1x1
    binDefinition = new BinDefinition(10.0, 10.0, 10.0, 100.0, 1.0, 1.0);
    bin = new Bin(binDefinition.getWidth(), binDefinition.getHeight(), binDefinition.getDepth());
    settings = new PackingRequest.AlgorithmSettingsDto(); // Default settings
    statusMessages = new ArrayList<>();

    // Standard Piece: 2x2x2, Weight 10
    testPiece = new Piece("P1", 2.0, 2.0, 2.0, 1.0, 10.0, true, true, true, 5, 0, 0, 10);
  }

  // --- Weight Constraint Tests ---

  @Test
  void checkPlacement_ValidWeight() {
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid with weight within limit");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty for valid placement");
  }

  @Test
  void checkPlacement_ExceedsMaxWeight() {
    // Place pieces until just under the weight limit
    Piece heavyPiece = new Piece("Heavy", 1, 1, 1, 1, 95.0, true, true, true, 1, 0, 0, 1);
    bin.placePiece(heavyPiece, new Position(0, 0, 0), 0); // Current weight = 95

    // Try to place the testPiece (weight 10)
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(1, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail when exceeding max weight");
    assertFalse(statusMessages.isEmpty(), "Status messages should contain weight violation");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Exceeds bin max weight")),
        "Status message should mention max weight violation");
  }

  @Test
  void checkPlacement_ExactlyMaxWeight() {
    Piece heavyPiece = new Piece("Heavy", 1, 1, 1, 1, 90.0, true, true, true, 1, 0, 0, 1);
    bin.placePiece(heavyPiece, new Position(0, 0, 0), 0); // Current weight = 90

    // Try to place the testPiece (weight 10)
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(1, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed when exactly at max weight");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty for valid placement");
  }

  // --- Overhang Constraint Tests ---

  @Test
  void checkPlacement_WithinBoundsNoOverhang() {
    // Place piece well within bounds
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid well within bounds");
  }

  @Test
  void checkPlacement_ExactlyAtWidthBoundary_NoOverhangAllowed() {
    // Bin 10x10x10, Piece 2x2x2
    BinDefinition noOverhangDef = new BinDefinition(10.0, 10.0, 10.0, 100.0, 0.0, 0.0);
    Bin noOverhangBin =
        new Bin(noOverhangDef.getWidth(), noOverhangDef.getHeight(), noOverhangDef.getDepth());
    // Place at x=8, piece ends exactly at x=10
    assertTrue(
        noOverhangBin.checkPlacement(
            testPiece, new Position(8, 0, 0), 0, noOverhangDef, settings, statusMessages),
        "Placement should be valid exactly at boundary with no overhang allowed");
  }

  @Test
  void checkPlacement_ExceedsWidthBoundary_NoOverhangAllowed() {
    // Bin 10x10x10, Piece 2x2x2
    BinDefinition noOverhangDef = new BinDefinition(10.0, 10.0, 10.0, 100.0, 0.0, 0.0);
    Bin noOverhangBin =
        new Bin(noOverhangDef.getWidth(), noOverhangDef.getHeight(), noOverhangDef.getDepth());
    // Place at x=8.1, piece ends at x=10.1
    assertFalse(
        noOverhangBin.checkPlacement(
            testPiece, new Position(8.1, 0, 0), 0, noOverhangDef, settings, statusMessages),
        "Placement should fail exceeding boundary with no overhang allowed");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Exceeds allowed width")),
        "Status message should mention width violation");
  }

  @Test
  void checkPlacement_WithinOverhangX() {
    // Bin 10x10x10, OverhangX 1.0 -> Allowed Width 11.0
    // Piece 2x2x2 placed at x=9.0, ends at x=11.0
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(9.0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid within X overhang");
  }

  @Test
  void checkPlacement_ExactlyAtOverhangXLimit() {
    // Bin 10x10x10, OverhangX 1.0 -> Allowed Width 11.0
    // Piece 2x2x2 placed at x=9.0, ends exactly at x=11.0
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(9.0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid exactly at X overhang limit");
  }

  @Test
  void checkPlacement_ExceedsOverhangX() {
    // Bin 10x10x10, OverhangX 1.0 -> Allowed Width 11.0
    // Piece 2x2x2 placed at x=9.1, ends at x=11.1
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(9.1, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail exceeding X overhang");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Exceeds allowed width")),
        "Status message should mention width violation");
  }

  @Test
  void checkPlacement_WithinOverhangY() { // Note: OverhangY corresponds to Depth (Z-axis)
    // Bin 10x10x10, OverhangY 1.0 -> Allowed Depth 11.0
    // Piece 2x2x2 placed at z=9.0, ends at z=11.0
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 9.0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid within Y overhang (Depth)");
  }

  @Test
  void checkPlacement_ExactlyAtOverhangYLimit() { // Note: OverhangY corresponds to Depth (Z-axis)
    // Bin 10x10x10, OverhangY 1.0 -> Allowed Depth 11.0
    // Piece 2x2x2 placed at z=9.0, ends exactly at z=11.0
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 9.0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid exactly at Y overhang limit (Depth)");
  }

  @Test
  void checkPlacement_ExceedsOverhangY() { // Note: OverhangY corresponds to Depth (Z-axis)
    // Bin 10x10x10, OverhangY 1.0 -> Allowed Depth 11.0
    // Piece 2x2x2 placed at z=9.1, ends at z=11.1
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 9.1), 0, binDefinition, settings, statusMessages),
        "Placement should fail exceeding Y overhang (Depth)");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Exceeds allowed depth")),
        "Status message should mention depth violation");
  }

  @Test
  void checkPlacement_ExceedsHeight() {
    // Bin 10x10x10, Height is strict (no overhang)
    // Piece 2x2x2 placed at y=8.1, ends at y=10.1
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(0, 8.1, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail exceeding height boundary");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Exceeds allowed height")),
        "Status message should mention height violation");
  }

  @Test
  void checkPlacement_ExactlyAtHeightLimit() {
    // Bin 10x10x10, Height is strict (no overhang)
    // Piece 2x2x2 placed at y=8.0, ends exactly at y=10.0
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 8.0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be valid exactly at height boundary");
  }

  // --- Stacking Limit Tests ---

  @Test
  void checkPlacement_StackingOnPieceWithLimitZero() {
    // Piece A: Stacking limit 0
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 0, 0, 0, 1); // Limit 0
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // Place A at floor

    // Try to place testPiece (P1) on top of A (at y=2)
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(0, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail when stacking on piece with limit 0");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("stacking limit")),
        "Status message should mention stacking limit violation");
  }

  @Test
  void checkPlacement_StackingOnPieceWithLimitOne_FirstStack() {
    // Piece A: Stacking limit 1
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 1, 0, 0, 1); // Limit 1
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // Place A at floor

    // Try to place testPiece (P1) on top of A (at y=2) - Should succeed
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed for first item on piece with limit 1");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty for valid placement");
  }

  @Test
  void checkPlacement_StackingOnPieceWithLimitOne_SecondStack() {
    // Piece A: Stacking limit 1
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 1, 0, 0, 1); // Limit 1
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // Place A at floor

    // Place first piece (P1) on top of A - Should succeed
    Piece pieceP1 = new Piece("P1", 2, 2, 2, 1, 10, true, true, true, 5, 0, 0, 10);
    bin.placePiece(pieceP1, new Position(0, 2, 0), 0); // Place P1 at y=2

    // Try to place second piece (P2) on top of A (also at y=2, next to P1) - Should fail based on
    // A's limit
    // Note: This tests the limit of the *supporting* piece (A), not the piece being placed.
    // The check happens *before* placement.
    Piece pieceP2 = new Piece("P2", 2, 2, 2, 1, 10, true, true, true, 5, 0, 0, 10);
    assertFalse(
        bin.checkPlacement(
            pieceP2, new Position(2, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail for second item when supporting piece limit is 1");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("stacking limit")),
        "Status message should mention stacking limit violation");
  }

  @Test
  void checkPlacement_StackingOnPieceWithHighLimit() {
    // Piece A: Stacking limit 5 (same as testPiece)
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1); // Limit 5
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // Place A at floor

    // Place first piece (P1) on top of A
    Piece pieceP1 = new Piece("P1", 2, 2, 2, 1, 10, true, true, true, 5, 0, 0, 10);
    bin.placePiece(pieceP1, new Position(0, 0, 2), 0); // Place P1 at z=2 (on A)
    statusMessages.clear(); // Clear messages after placing P1

    // Try to place second piece (P2) on top of P1 - Should succeed
    Piece pieceP2 = new Piece("P2", 2, 2, 2, 1, 10, true, true, true, 5, 0, 0, 10);
    assertTrue(
        bin.checkPlacement(
            pieceP2, new Position(0, 0, 4), 0, binDefinition, settings, statusMessages),
        "Placement should succeed for second item when supporting piece limit is high");
    assertTrue(
        statusMessages.isEmpty(), "Status messages should be empty for valid placement check");
  }

  // --- Load Priority Tests ---

  @Test
  void checkPlacement_PlacingLowerPriorityOnHigherPriority() {
    // Piece A: Priority 1
    Piece pieceA_Prio1 = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 1, 0, 1); // Prio 1
    bin.placePiece(pieceA_Prio1, new Position(0, 0, 0), 0); // Place A at floor

    // testPiece (P1) has Priority 0 (default)
    // Try to place P1 (Prio 0) on top of A (Prio 1) - Should succeed
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed placing lower priority (0) on higher priority (1)");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty for valid placement");
  }

  @Test
  void checkPlacement_PlacingHigherPriorityOnLowerPriority() {
    // Piece A: Priority 0 (default)
    Piece pieceA_Prio0 = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1); // Prio 0
    bin.placePiece(pieceA_Prio0, new Position(0, 0, 0), 0); // Place A at floor

    // Piece P2: Priority 1
    Piece pieceP2_Prio1 = new Piece("P2", 2, 2, 2, 1, 10, true, true, true, 5, 1, 0, 10); // Prio 1

    // Try to place P2 (Prio 1) on top of A (Prio 0) - Should fail
    assertFalse(
        bin.checkPlacement(
            pieceP2_Prio1, new Position(0, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail placing higher priority (1) on lower priority (0)");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Cannot place on lower priority")),
        "Status message should mention load priority violation");
  }

  @Test
  void checkPlacement_PlacingEqualPriorityOnEqualPriority() {
    // Piece A: Priority 1
    Piece pieceA_Prio1 = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 1, 0, 1); // Prio 1
    bin.placePiece(pieceA_Prio1, new Position(0, 0, 0), 0); // Place A at floor

    // Piece P2: Priority 1
    Piece pieceP2_Prio1 = new Piece("P2", 2, 2, 2, 1, 10, true, true, true, 5, 1, 0, 10); // Prio 1

    // Try to place P2 (Prio 1) on top of A (Prio 1) - Should succeed
    assertTrue(
        bin.checkPlacement(
            pieceP2_Prio1, new Position(0, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed placing equal priority (1) on equal priority (1)");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty for valid placement");
  }

  // --- Orientation Constraint Tests ---

  @Test
  void checkPlacement_AllowedOrientation() {
    // testPiece allows all orientations by default
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed with default allowed orientation (0)");
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 0), 1, binDefinition, settings, statusMessages),
        "Placement should succeed with default allowed orientation (1)");
    // ... potentially check all 6 if needed
  }

  @Test
  void checkPlacement_DisallowedOrientation() {
    // Piece NoRotZ: 3x2x1, allowRotationX=true, allowRotationY=true, allowRotationZ=false
    // Orientations 1, 3, 5 involve Z rotation based on Piece.java logic
    Piece pieceNoRotZ = new Piece("NoZ", 3, 2, 1, 1, 5, true, true, false, 5, 0, 0, 1);

    // Orientation 0 (WHD -> 3x2x1) - Allowed
    assertTrue(
        bin.checkPlacement(
            pieceNoRotZ, new Position(0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed for orientation 0 (Original)");
    statusMessages.clear();

    // Orientation 1 (WHD -> HWD -> 2x3x1) - DISALLOWED (Requires Z rotation)
    assertFalse(
        bin.checkPlacement(
            pieceNoRotZ, new Position(0, 0, 0), 1, binDefinition, settings, statusMessages),
        "Placement should fail for orientation 1 (Z rotation disallowed)");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Orientation 1 is not allowed")),
        "Status message should mention orientation 1 violation");
    statusMessages.clear();

    // Orientation 2 (WHD -> WDH -> 3x1x2) - Allowed (Requires Y rotation)
    assertTrue(
        bin.checkPlacement(
            pieceNoRotZ, new Position(0, 0, 0), 2, binDefinition, settings, statusMessages),
        "Placement should succeed for orientation 2 (Y rotation allowed)");
    statusMessages.clear();

    // Orientation 3 (WHD -> DWH -> 1x3x2) - DISALLOWED (Requires Y and Z rotation)
    assertFalse(
        bin.checkPlacement(
            pieceNoRotZ, new Position(0, 0, 0), 3, binDefinition, settings, statusMessages),
        "Placement should fail for orientation 3 (Z rotation disallowed)");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Orientation 3 is not allowed")),
        "Status message should mention orientation 3 violation");
    statusMessages.clear();

    // Orientation 4 (WHD -> HDW -> 2x1x3) - Allowed (Requires X rotation)
    assertTrue(
        bin.checkPlacement(
            pieceNoRotZ, new Position(0, 0, 0), 4, binDefinition, settings, statusMessages),
        "Placement should succeed for orientation 4 (X rotation allowed)");
    statusMessages.clear();

    // Orientation 5 (WHD -> D H W -> 1x2x3) - DISALLOWED (Requires X and Z rotation)
    assertFalse(
        bin.checkPlacement(
            pieceNoRotZ, new Position(0, 0, 0), 5, binDefinition, settings, statusMessages),
        "Placement should fail for orientation 5 (Z rotation disallowed)");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Orientation 5 is not allowed")),
        "Status message should mention orientation 5 violation");
  }

  // --- Stability Tests ---

  @Test
  void checkPlacement_StableOnFloor() {
    // Placing on the floor (y=0) bypasses the stability check
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be stable on the bin floor");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty");
  }

  @Test
  void checkPlacement_StableOnSingleLargerPiece() {
    // Piece A: 4x2x4, placed at floor
    Piece pieceA = new Piece("A", 4, 2, 4, 1, 10, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // Place A at 0,0,0 (covers 0,0,0 to 4,2,4)

    // testPiece (P1) is 2x2x2
    // Place P1 centered on top of A (at y=2)
    // P1 corners: (1,2,1), (3,2,1), (1,2,3), (3,2,3)
    // A top surface: x=[0,4], z=[0,4]
    // All corners of P1 are supported by A.
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(1, 2, 1), 0, binDefinition, settings, statusMessages),
        "Placement should be stable when fully supported by one piece below");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty");
  }

  @Test
  void checkPlacement_StableOnTwoAdjacentPieces() {
    // Piece A: 2x2x2 at 0,0,0
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0);
    // Piece B: 2x2x2 at 2,0,0
    Piece pieceB = new Piece("B", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceB, new Position(2, 0, 0), 0);
    // A covers x=[0,2], B covers x=[2,4] at y=2 top surface

    // testPiece (P1) is 2x2x2
    // Place P1 bridging A and B (at y=2)
    // P1 corners: (1,2,0), (3,2,0), (1,2,2), (3,2,2)
    // Corner (1,2,0) supported by A
    // Corner (3,2,0) supported by B
    // Corner (1,2,2) supported by A
    // Corner (3,2,2) supported by B
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(1, 2, 0), 0, binDefinition, settings, statusMessages),
        "Placement should be stable when supported by two adjacent pieces below");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty");
  }

  @Test
  void checkPlacement_UnstableOneCornerUnsupported() {
    // Piece A: 2x2x2 at 0,0,0
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // A top surface y=2, x=[0,2], z=[0,2]

    // testPiece (P1) is 2x2x2
    // Place P1 partially hanging off A (at y=2)
    // P1 corners: (1,2,1), (3,2,1), (1,2,3), (3,2,3)
    // Corner (1,2,1) supported by A
    // Corner (3,2,1) NOT supported (x=3 > A.maxX=2)
    // Corner (1,2,3) NOT supported (z=3 > A.maxZ=2)
    // Corner (3,2,3) NOT supported
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(1, 2, 1), 0, binDefinition, settings, statusMessages),
        "Placement should be unstable with unsupported corners");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Unstable")),
        "Status message should mention stability violation");
  }

  // --- Overlap Tests ---

  @Test
  void checkPlacement_NoOverlap() {
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0);

    // Place testPiece next to A
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(2, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed with no overlap");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty");
  }

  @Test
  void checkPlacement_OverlapX() {
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // A is at x=[0,2]

    // Try to place testPiece (2x2x2) starting at x=1 (overlaps A)
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(1, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail due to X overlap");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Overlaps")),
        "Status message should mention overlap violation");
  }

  @Test
  void checkPlacement_OverlapY() {
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // A is at y=[0,2]

    // Try to place testPiece (2x2x2) starting at y=1 (overlaps A)
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(0, 1, 0), 0, binDefinition, settings, statusMessages),
        "Placement should fail due to Y overlap");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Overlaps")),
        "Status message should mention overlap violation");
  }

  @Test
  void checkPlacement_OverlapZ() {
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // A is at z=[0,2]

    // Try to place testPiece (2x2x2) starting at z=1 (overlaps A)
    assertFalse(
        bin.checkPlacement(
            testPiece, new Position(0, 0, 1), 0, binDefinition, settings, statusMessages),
        "Placement should fail due to Z overlap");
    assertTrue(
        statusMessages.stream().anyMatch(m -> m.contains("Overlaps")),
        "Status message should mention overlap violation");
  }

  @Test
  void checkPlacement_TouchingFacesNoOverlap() {
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0); // A ends at x=2

    // Place testPiece exactly at x=2 (touching A's face)
    assertTrue(
        bin.checkPlacement(
            testPiece, new Position(2, 0, 0), 0, binDefinition, settings, statusMessages),
        "Placement should succeed when faces are touching (no volumetric overlap)");
    assertTrue(statusMessages.isEmpty(), "Status messages should be empty");
  }

  // --- placePiece Method Tests ---

  @Test
  void placePiece_UpdatesCurrentWeight() {
    assertEquals(0.0, bin.getCurrentWeight(), "Initial weight should be 0");

    // Place testPiece (weight 10)
    bin.placePiece(testPiece, new Position(0, 0, 0), 0);
    assertEquals(10.0, bin.getCurrentWeight(), "Weight should be 10 after placing first piece");

    // Place another piece (weight 5)
    Piece pieceA = new Piece("A", 1, 1, 1, 1, 5.0, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(2, 0, 0), 0);
    assertEquals(15.0, bin.getCurrentWeight(), "Weight should be 15 after placing second piece");
  }

  @Test
  void placePiece_IncrementsStackingCountOnSupportingPiece() {
    // Piece A: Stacking limit 0
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 0, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0);
    assertEquals(
        0,
        pieceA.getPiecesStackedOnTopCount(),
        "Piece A should initially have 0 pieces stacked on top");

    // Place testPiece (P1) on top of A
    bin.placePiece(testPiece, new Position(0, 2, 0), 0);

    // Verify stack count on pieceA is incremented
    assertEquals(
        1,
        pieceA.getPiecesStackedOnTopCount(),
        "Piece A should have 1 piece stacked on top after placing P1");
  }

  @Test
  void placePiece_IncrementsStackingCountOnMultipleSupportingPieces() {
    // Piece A: 2x2x2 at 0,0,0
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0);
    // Piece B: 2x2x2 at 2,0,0
    Piece pieceB = new Piece("B", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceB, new Position(2, 0, 0), 0);

    assertEquals(
        0,
        pieceA.getPiecesStackedOnTopCount(),
        "Piece A should initially have 0 pieces stacked on top");
    assertEquals(
        0,
        pieceB.getPiecesStackedOnTopCount(),
        "Piece B should initially have 0 pieces stacked on top");

    // Place testPiece (P1) bridging A and B (at y=2)
    bin.placePiece(testPiece, new Position(1, 2, 0), 0);

    // Verify stack count on both pieceA and pieceB is incremented
    assertEquals(
        1,
        pieceA.getPiecesStackedOnTopCount(),
        "Piece A should have 1 piece stacked on top after placing P1");
    assertEquals(
        1,
        pieceB.getPiecesStackedOnTopCount(),
        "Piece B should have 1 piece stacked on top after placing P1");
  }

  @Test
  void placePiece_DoesNotIncrementStackingCountOnNonSupportingPiece() {
    // Piece A: 2x2x2 at 0,0,0
    Piece pieceA = new Piece("A", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceA, new Position(0, 0, 0), 0);
    // Piece C: 2x2x2 at 5,0,0 (not under the new piece)
    Piece pieceC = new Piece("C", 2, 2, 2, 1, 5, true, true, true, 5, 0, 0, 1);
    bin.placePiece(pieceC, new Position(5, 0, 0), 0);

    assertEquals(
        0,
        pieceA.getPiecesStackedOnTopCount(),
        "Piece A should initially have 0 pieces stacked on top");
    assertEquals(
        0,
        pieceC.getPiecesStackedOnTopCount(),
        "Piece C should initially have 0 pieces stacked on top");

    // Place testPiece (P1) on top of A only
    bin.placePiece(testPiece, new Position(0, 2, 0), 0);

    // Verify stack count on pieceA is incremented, but not on pieceC
    assertEquals(
        1,
        pieceA.getPiecesStackedOnTopCount(),
        "Piece A should have 1 piece stacked on top after placing P1");
    assertEquals(
        0,
        pieceC.getPiecesStackedOnTopCount(),
        "Piece C should still have 0 pieces stacked on top");
  }

  // --- Volume Utilisation Tests ---

  // Helper method to set placedPieces using reflection
  private void setPlacedPieces(Bin bin, List<Piece> pieces) throws Exception {
    // Ensure we can access the private field
    Field placedPiecesField = Bin.class.getDeclaredField("placedPieces");
    placedPiecesField.setAccessible(true);
    // Set the field with a modifiable list copy
    placedPiecesField.set(bin, new ArrayList<>(pieces));
  }

  @Test
  void getVolumeUtilisation_EmptyBin() throws Exception {
    Bin emptyBin = new Bin(10, 10, 10);
    setPlacedPieces(emptyBin, new ArrayList<>());
    assertEquals(
        0.0, emptyBin.getVolumeUtilisation(), 0.001, "Utilisation should be 0% for an empty bin");
  }

  @Test
  void getVolumeUtilisation_PartiallyFilled() throws Exception {
    // Bin volume = 10 * 10 * 10 = 1000
    Bin partialBin = new Bin(10, 10, 10);
    Piece p1 = new Piece("P1", 5, 5, 5, 1, 10, true, true, true, 1, 0, 0, 1); // Volume = 125
    Piece p2 = new Piece("P2", 2, 3, 4, 1, 5, true, true, true, 1, 0, 0, 1); // Volume = 24
    setPlacedPieces(partialBin, Arrays.asList(p1, p2));
    // Total volume = 125 + 24 = 149
    // Expected utilisation = (149 / 1000) * 100 = 14.9%
    assertEquals(
        14.9,
        partialBin.getVolumeUtilisation(),
        0.001,
        "Utilisation calculation is incorrect for partial fill");
  }

  @Test
  void getVolumeUtilisation_CompletelyFilled() throws Exception {
    // Bin volume = 10 * 10 * 10 = 1000
    Bin fullBin = new Bin(10, 10, 10);
    Piece p1 = new Piece("P1", 10, 10, 10, 1, 100, true, true, true, 1, 0, 0, 1); // Volume = 1000
    setPlacedPieces(fullBin, Arrays.asList(p1));
    // Expected utilisation = (1000 / 1000) * 100 = 100%
    assertEquals(
        100.0,
        fullBin.getVolumeUtilisation(),
        0.001,
        "Utilisation calculation should be 100% for a full bin");
  }

  @Test
  void getVolumeUtilisation_OverfilledTheoretically() throws Exception {
    // This tests the clamping, though ideally placement logic prevents this
    // Bin volume = 2 * 2 * 2 = 8
    Bin smallBin = new Bin(2, 2, 2);
    Piece p1 = new Piece("P1", 3, 3, 3, 1, 10, true, true, true, 1, 0, 0, 1); // Volume = 27
    setPlacedPieces(smallBin, Arrays.asList(p1));
    // Raw calculation = (27 / 8) * 100 = 337.5%
    // Expected clamped value = 100%
    assertEquals(
        100.0,
        smallBin.getVolumeUtilisation(),
        0.001,
        "Utilisation should be clamped at 100% even if theoretical item volume exceeds bin volume");
  }

  // --- Intersection and Placement Tests ---
}
