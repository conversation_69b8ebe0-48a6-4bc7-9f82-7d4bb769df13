{"name": "Test Rule Template", "description": "A simple test rule for the rule editor", "algorithmType": "FirstFitDecreasingVolume", "bin": {"width": 1000, "height": 1000, "depth": 1000, "maxWeight": 100}, "boxTypes": [{"id": "box1", "width": 200, "height": 200, "depth": 200, "weight": 5, "quantity": 10, "shapeType": "Cuboid"}, {"id": "box2", "width": 300, "height": 150, "depth": 150, "weight": 3, "quantity": 5, "shapeType": "Cuboid"}], "algorithmSettings": {"allowRotation": true}}