package com.packprime.innovademo;

import static org.junit.jupiter.api.Assertions.*;

import com.packprime.innovademo.algorithm.Bin;
import com.packprime.innovademo.algorithm.MaximalRectanglesAlgorithm;
import com.packprime.innovademo.algorithm.PackingResult;
import com.packprime.innovademo.algorithm.Piece;
import com.packprime.innovademo.model.BinDefinition;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MaximalRectanglesAlgorithmTest {

  private MaximalRectanglesAlgorithm algorithm;

  @BeforeEach
  void setUp() {
    algorithm = new MaximalRectanglesAlgorithm();
  }

  // Helper to create a default BinDefinition for tests
  private BinDefinition createDefaultBinDef(double maxWeight) {
    BinDefinition binDef = new BinDefinition();
    binDef.setMaxWeight(maxWeight);
    // Assuming no overhang for standard tests
    binDef.setOverhangX(0);
    binDef.setOverhangY(0);
    return binDef;
  }

  // Helper to create a Piece with default constraints for tests
  private Piece createPiece(String id, double w, double h, double d, double weight) {
    // Using default values for value, rotation, stacking, priority, quantity
    return new Piece(id, w, h, d, 0.0, weight, true, true, true, 99, 0, 1, 1);
  }

  @Test
  void testPackSinglePieceFits() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    Piece piece = createPiece("P1", 5, 5, 5, 10.0);
    List<Piece> pieces = Collections.singletonList(piece);

    // Act
    PackingResult result =
        algorithm.pack(bin, pieces, binDef, null); // Algorithm settings DTO is null for now

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getPlacedPieces().size());
    assertTrue(result.getUnplacedPieces().isEmpty());
    Piece placedPiece = result.getPlacedPieces().get(0);
    assertEquals("P1", placedPiece.getId());
    assertNotNull(placedPiece.getPosition());
    // Maximal Rectangles might place it differently than just 0,0,0 depending on MES logic,
    // but for the first piece in an empty bin, origin is the most likely.
    assertEquals(0, placedPiece.getPosition().getX());
    assertEquals(0, placedPiece.getPosition().getY());
    assertEquals(0, placedPiece.getPosition().getZ());
    // Default orientation is 0
    assertEquals(0, placedPiece.getOrientation());
  }

  @Test
  void testPackPieceTooLargeForBin() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    Piece piece = createPiece("P1", 12, 5, 5, 10.0); // Width is too large
    List<Piece> pieces = Collections.singletonList(piece);

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertTrue(result.getPlacedPieces().isEmpty());
    assertEquals(1, result.getUnplacedPieces().size());
    assertEquals("P1", result.getUnplacedPieces().get(0).getId());
  }

  @Test
  void testPackPieceTooHeavyForBin() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(5.0); // Max weight 5.0
    Piece piece = createPiece("P1", 5, 5, 5, 10.0); // Weight is too large
    List<Piece> pieces = Collections.singletonList(piece);

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    // Note: The current algorithm doesn't check weight constraints explicitly during packing!
    // This test *should* fail based on the description, but might pass with current code.
    // We will add assertions assuming the algorithm *should* check weight.
    assertTrue(result.getPlacedPieces().isEmpty(), "Piece should not be placed if too heavy.");
    assertEquals(1, result.getUnplacedPieces().size(), "Piece should be unplaced if too heavy.");
    assertEquals("P1", result.getUnplacedPieces().get(0).getId());
    // TODO: Verify if weight check needs to be added to the algorithm itself.
  }

  @Test
  void testPackEmptyPieceList() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    List<Piece> pieces = Collections.emptyList();

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertTrue(result.getPlacedPieces().isEmpty());
    assertTrue(result.getUnplacedPieces().isEmpty());
  }

  @Test
  void testPackTwoPiecesFit() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    Piece piece1 = createPiece("P1", 4, 4, 4, 5.0);
    Piece piece2 = createPiece("P2", 4, 4, 4, 5.0);
    List<Piece> pieces = Arrays.asList(piece1, piece2);

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertEquals(2, result.getPlacedPieces().size(), "Should place both pieces");
    assertTrue(result.getUnplacedPieces().isEmpty(), "Should have no unplaced pieces");
    // Check IDs (order might vary depending on MES selection)
    assertTrue(result.getPlacedPieces().stream().anyMatch(p -> p.getId().equals("P1")));
    assertTrue(result.getPlacedPieces().stream().anyMatch(p -> p.getId().equals("P2")));
  }

  @Test
  void testPackSecondPieceRequiresRotation() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    Piece piece1 = createPiece("P1", 8, 8, 2, 10.0); // Takes up 8x8 footprint
    Piece piece2 = createPiece("P2", 2, 2, 9, 5.0); // Fits only if rotated (e.g., 9x2x2 or 2x9x2)
    List<Piece> pieces = Arrays.asList(piece1, piece2);

    // Act
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    assertEquals(2, result.getPlacedPieces().size(), "Should place both pieces");
    assertTrue(result.getUnplacedPieces().isEmpty(), "Should have no unplaced pieces");

    Piece placedP1 =
        result.getPlacedPieces().stream()
            .filter(p -> p.getId().equals("P1"))
            .findFirst()
            .orElse(null);
    Piece placedP2 =
        result.getPlacedPieces().stream()
            .filter(p -> p.getId().equals("P2"))
            .findFirst()
            .orElse(null);

    assertNotNull(placedP1);
    assertNotNull(placedP2);

    // P1 should be at origin, no rotation
    assertEquals(0, placedP1.getPosition().getX());
    assertEquals(0, placedP1.getPosition().getY());
    assertEquals(0, placedP1.getPosition().getZ());
    assertEquals(0, placedP1.getOrientation());

    // P2 should be placed somewhere. Its position and orientation depend
    // on the specific MES generated and chosen after P1 was placed.
    // We primarily care that it *was* successfully placed.
    assertNotNull(placedP2.getPosition(), "P2 should have a valid position");
  }

  @Test
  void testPackThreePiecesThirdDoesntFit() {
    // Arrange
    Bin bin = new Bin(10, 10, 10);
    BinDefinition binDef = createDefaultBinDef(100.0);
    Piece p1 = createPiece("P1", 6, 6, 6, 10.0);
    Piece p2 = createPiece("P2", 6, 6, 6, 10.0); // Two large pieces
    Piece p3 = createPiece("P3", 3, 3, 3, 5.0); // Small piece
    List<Piece> pieces = Arrays.asList(p1, p2, p3);

    // Act
    // Note: Depending on placement, two 6x6x6 might not fit in 10x10x10.
    // Let's assume P1 fits at 0,0,0. The next MES might not allow P2.
    // Re-evaluate this test based on expected MES behavior if it fails.
    PackingResult result = algorithm.pack(bin, pieces, binDef, null);

    // Assert
    assertNotNull(result);
    // However, P3 (smaller) *should* fit in the space remaining after P1.
    // The algorithm tries pieces in descending volume order (P1, P2, P3).
    // It places P1. It fails to place P2. It succeeds in placing P3.
    assertEquals(2, result.getPlacedPieces().size(), "Should place P1 and P3");
    assertEquals(1, result.getUnplacedPieces().size(), "Should have one unplaced piece (P2)");

    // Verify placed pieces (order might vary)
    assertTrue(
        result.getPlacedPieces().stream().anyMatch(p -> p.getId().equals("P1")),
        "P1 should be placed");
    assertTrue(
        result.getPlacedPieces().stream().anyMatch(p -> p.getId().equals("P3")),
        "P3 should be placed");

    // Verify unplaced piece
    assertEquals(
        "P2", result.getUnplacedPieces().get(0).getId(), "P2 should be the unplaced piece");
  }

  // TODO: Add more tests:
  // - Specific MES splitting scenarios
  // - Bin nearly full / full

}
