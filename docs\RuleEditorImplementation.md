# Rule Editor Implementation Plan

## Overview

This document outlines the implementation plan for a hybrid rule editor in the InnovaDemo project. The editor will allow users to create, modify, and test packing algorithm rules through both form-based editing and direct JSON manipulation.

## Core Features

1. **Template Gallery**: Predefined rule templates as starting points
2. **Form-Based Editing**: Structured interface for common parameters
3. **JSON Editor**: Advanced editing capabilities for power users
4. **Rule Storage**: Save and load custom rules
5. **Testing Interface**: Visualize and test rules

## Technical Architecture

### Storage Strategy

- **Built-in Templates**: Stored in `src\main\resources\static\industry_data\` (read-only)
- **User-Created Rules**: Stored in a dedicated directory: `user_rules\`
  - This directory will be created at the application root
  - Backend API will handle reading/writing to this directory
  - Files will use naming convention: `user_[timestamp]_[name].json`

### Frontend Components

1. **Template Gallery Component**
   - Grid of template cards with previews
   - Filter and search functionality
   - Template selection and loading

2. **Form Editor Component**
   - Bin configuration section
   - Primary packs editor
   - Algorithm settings
   - Constraints builder

3. **JSON Editor Component**
   - Monaco Editor integration
   - JSON schema validation
   - Syntax highlighting and error detection

4. **Rule Management Component**
   - Save/load interface
   - Export/import functionality
   - Rule history and versioning

5. **Testing Interface**
   - Integration with existing visualization
   - Results analysis
   - Comparison tools

### Backend Services

1. **Rule Template Service**
   - List available templates
   - Load template data

2. **User Rule Service**
   - Create user rule directory if not exists
   - Save user rules to disk
   - List user-created rules
   - Load user rule data
   - Delete user rules

3. **Rule Validation Service**
   - Validate rule syntax and semantics
   - Provide validation feedback

## Implementation Steps

### Phase 1: Foundation

1. Create backend endpoints for rule management
   - GET `/api/rules/templates` - List built-in templates
   - GET `/api/rules/user` - List user-created rules
   - GET `/api/rules/template/{id}` - Get template details
   - GET `/api/rules/user/{id}` - Get user rule details
   - POST `/api/rules/user` - Save user rule
   - DELETE `/api/rules/user/{id}` - Delete user rule

2. Set up storage mechanism for user rules
   - Create directory structure
   - Implement file I/O operations
   - Add error handling

### Phase 2: UI Components

1. Create template gallery page/component
   - Design card layout
   - Implement template loading
   - Add filtering and search

2. Build form editor component
   - Design form layout
   - Implement form validation
   - Create dynamic form fields

3. Integrate JSON editor
   - Set up Monaco Editor
   - Configure JSON schema
   - Implement syntax validation

4. Develop rule management UI
   - Create save/load interface
   - Implement export/import
   - Add version tracking

### Phase 3: Integration

1. Connect form editor and JSON editor
   - Sync state between editors
   - Handle transitions between views

2. Integrate with visualization
   - Connect rule editor to test runner
   - Update visualization with new rules
   - Add results analysis

3. Implement local storage backup
   - Auto-save drafts
   - Recover from browser crashes

### Phase 4: Refinement

1. Add user experience enhancements
   - Tooltips and help text
   - Onboarding guide
   - Keyboard shortcuts

2. Optimize performance
   - Lazy loading components
   - Caching strategies
   - Reduce re-renders

3. Add advanced features
   - Rule comparison
   - Rule templates from user rules
   - Sharing capabilities

## UI Mockup

```
+----------------------------------------------------------+
| [Templates] [Form Editor] [JSON] [Test]                  |
+----------------------------------------------------------+
|                                                          |
|        Content based on active tab                       |
|                                                          |
|                                                          |
|                                                          |
|                                                          |
|                                                          |
+----------------------------------------------------------+
|           Save | Export | Run Test                       |
+----------------------------------------------------------+
```

## Technologies

- **Frontend**:
  - React/Next.js
  - Tailwind CSS
  - shadcn/ui components
  - Monaco Editor
  - React Hook Form + Zod

- **Backend**:
  - Spring Boot
  - Jackson for JSON processing
  - File I/O for rule storage

## Timeline

- **Phase 1**: 1-2 weeks
- **Phase 2**: 2-3 weeks
- **Phase 3**: 1-2 weeks
- **Phase 4**: 1 week

Total estimated time: 5-8 weeks

## Future Enhancements

- Cloud storage integration
- User accounts and rule sharing
- Rule analytics and optimization suggestions
- AI-assisted rule generation
- Rule version control and comparison
