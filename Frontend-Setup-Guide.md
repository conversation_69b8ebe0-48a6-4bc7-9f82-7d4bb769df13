# Frontend Setup Guide

This guide will walk you through setting up the Next.js frontend application for the packing algorithm visualization.

## Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Your Java backend running (typically on port 8080)

## Setup Steps

### 1. Create the Frontend Directory

```powershell
mkdir -p d:\work-projects\innovademo\frontend
cd d:\work-projects\innovademo\frontend
```

### 2. Initialize Next.js Project

```powershell
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
```

When prompted, select:
- Would you like to use TypeScript? › Yes
- Would you like to use ESLint? › Yes
- Would you like to use Tailwind CSS? › Yes
- Would you like to use `src/` directory? › Yes
- Would you like to use App Router? › Yes
- Would you like to customize the default import alias (@/*)? › Yes

### 3. Install Dependencies

```powershell
npm install three @types/three @react-three/fiber @react-three/drei
```

### 4. Install shadcn/ui

```powershell
npx shadcn-ui@latest init
```

When prompted, select:
- Would you like to use TypeScript? › Yes
- Which style would you like to use? › Default
- Which color would you like to use as base color? › Blue
- Where is your global CSS file? › src/app/globals.css
- Do you want to use CSS variables for colors? › Yes
- Where is your tailwind.config.js located? › tailwind.config.js
- Configure the import alias for components? › @/components
- Configure the import alias for utils? › @/lib/utils
- Are you using React Server Components? › No

### 5. Install Required shadcn/ui Components

```powershell
npx shadcn-ui@latest add button
npx shadcn-ui@latest add card
npx shadcn-ui@latest add form
npx shadcn-ui@latest add input
npx shadcn-ui@latest add table
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add separator
npx shadcn-ui@latest add alert
npx shadcn-ui@latest add tooltip
```

### 6. Create Directory Structure

```powershell
mkdir -p src/components/ui
mkdir -p src/components/forms
mkdir -p src/components/tables
mkdir -p src/components/three
mkdir -p src/components/layout
mkdir -p src/lib
mkdir -p src/types
```

### 7. Copy Files to Project

Copy the following files to their respective locations:

1. Copy `frontend-types.ts` to `src/types/index.ts`
2. Copy `frontend-api-service.ts` to `src/lib/api.ts`
3. Copy `frontend-visualization.tsx` to `src/components/three/packing-visualization.tsx`
4. Copy `frontend-form-components.tsx` to `src/components/forms/packing-form.tsx`
5. Copy `frontend-results-table.tsx` to `src/components/tables/results-table.tsx`
6. Copy `frontend-main-app.tsx` to `src/app/page.tsx`

### 8. Update the API Base URL

In `src/lib/api.ts`, update the `API_BASE_URL` constant to match your backend URL:

```typescript
const API_BASE_URL = 'http://localhost:8080/api';
```

### 9. Configure Tailwind for Dark Mode

Update `tailwind.config.js` to include dark mode support:

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
    },
  },
  plugins: [],
}
```

### 10. Update `globals.css` with Blue Theme Colors

Update `src/app/globals.css` to include the blue theme:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

### 11. Create a Layout Component

Create `src/app/layout.tsx`:

```tsx
import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Packing Visualizer',
  description: 'Visualize packing algorithms for primary and secondary packs',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
```

### 12. Start the Development Server

```powershell
npm run dev
```

This will start the Next.js development server, typically on port 3000. You can access the application at http://localhost:3000.

## Troubleshooting

### CORS Issues

If you encounter CORS issues when connecting to your Java backend, you'll need to configure CORS on your backend. Add the following configuration to your Spring Boot application:

```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
            .allowedOrigins("http://localhost:3000")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true);
    }
}
```

### Three.js Import Issues

If you encounter issues with Three.js imports, make sure to update your `next.config.js` to include transpilation for the Three.js modules:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['three'],
}

module.exports = nextConfig
```

## Next Steps

1. Update the components to match your specific requirements
2. Customize the UI styling to fit your preferences
3. Add additional features like exporting results or more visualization options
4. Test with your backend API to ensure proper integration

## Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com)
- [Three.js Documentation](https://threejs.org/docs)
