/**
 * Main Page Component
 * This is the entry point for the application
 */

"use client"

import React, { useState } from 'react';
import MainLayout from '@/components/layout/main-layout';
import SecondaryPackForm from '@/components/forms/secondary-pack-form';
import PrimaryPackForm from '@/components/forms/primary-pack-form';
import PackingVisualization from '@/components/three/packing-visualization';
import ResultsTable from '@/components/tables/results-table';
import { usePacking } from '@/hooks/use-packing';
import { ThemeProvider } from '@/components/theme/theme-provider';

export default function Home() {
  const {
    formState,
    setFormState,
    packingResult,
    isLoading,
    error,
    handlePack,
    handleSaveConfiguration,
    handleLoadConfiguration,
    savedConfigurations,
  } = usePacking();

  // State for save/load dialogs
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isLoadDialogOpen, setIsLoadDialogOpen] = useState(false);
  const [configurationName, setConfigurationName] = useState('');

  // Handle secondary pack changes
  const handleSecondaryPackChange = (secondaryPack: typeof formState.secondaryPack) => {
    setFormState({
      ...formState,
      secondaryPack,
    });
  };

  // Handle primary packs changes
  const handlePrimaryPacksChange = (primaryPacks: typeof formState.primaryPacks) => {
    setFormState({
      ...formState,
      primaryPacks,
    });
  };

  // Create a mapping of primary pack IDs to names for the results table
  const primaryPackNames: Record<string, string> = {};
  formState.primaryPacks.forEach(pack => {
    primaryPackNames[pack.id] = pack.name;
  });

  return (
    <ThemeProvider defaultTheme="dark">
      <MainLayout>
        {/* Error message */}
        {error && (
          <div className="mb-6 bg-red-100 dark:bg-red-900/20 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-400">Error</h3>
            <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Split view layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left column: Forms */}
          <div className="space-y-6">
            <SecondaryPackForm
              value={formState.secondaryPack}
              onChange={handleSecondaryPackChange}
            />
            
            <PrimaryPackForm
              value={formState.primaryPacks}
              onChange={handlePrimaryPacksChange}
            />
            
            <div className="flex justify-between">
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsSaveDialogOpen(true)}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                >
                  Save
                </button>
                <button
                  onClick={() => setIsLoadDialogOpen(true)}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                >
                  Load
                </button>
              </div>
              
              <button
                onClick={handlePack}
                disabled={
                  isLoading || 
                  !formState.secondaryPack.width || 
                  !formState.secondaryPack.height || 
                  !formState.secondaryPack.depth || 
                  !formState.secondaryPack.maxWeight ||
                  formState.primaryPacks.length === 0
                }
                className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${
                  isLoading || 
                  !formState.secondaryPack.width || 
                  !formState.secondaryPack.height || 
                  !formState.secondaryPack.depth || 
                  !formState.secondaryPack.maxWeight ||
                  formState.primaryPacks.length === 0
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800'
                }`}
              >
                {isLoading ? 'Packing...' : 'Pack Items'}
              </button>
            </div>
          </div>

          {/* Right column: Visualization */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <h2 className="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">3D Visualization</h2>
              <div className="h-[500px]">
                <PackingVisualization
                  secondaryPack={formState.secondaryPack}
                  packingResult={packingResult}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results table (full width) */}
        <div className="mt-8">
          <ResultsTable
            packingResult={packingResult}
            primaryPackNames={primaryPackNames}
          />
        </div>

        {/* Save dialog */}
        {isSaveDialogOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Save Configuration
              </h3>
              <div>
                <label htmlFor="configName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Configuration Name
                </label>
                <input
                  type="text"
                  id="configName"
                  value={configurationName}
                  onChange={(e) => setConfigurationName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter configuration name"
                />
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setIsSaveDialogOpen(false);
                    setConfigurationName('');
                  }}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    handleSaveConfiguration(configurationName);
                    setIsSaveDialogOpen(false);
                    setConfigurationName('');
                  }}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Load dialog */}
        {isLoadDialogOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Load Configuration
              </h3>
              {savedConfigurations.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400">No saved configurations found.</p>
              ) : (
                <div className="max-h-60 overflow-y-auto">
                  <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                    {savedConfigurations.map((config) => (
                      <li key={config.name} className="py-2">
                        <button
                          onClick={() => {
                            handleLoadConfiguration(config.name);
                            setIsLoadDialogOpen(false);
                          }}
                          className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
                        >
                          <div className="font-medium text-gray-900 dark:text-white">{config.name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(config.timestamp).toLocaleString()}
                          </div>
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setIsLoadDialogOpen(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </MainLayout>
    </ThemeProvider>
  );
}
