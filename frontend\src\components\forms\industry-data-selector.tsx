/**
 * Industry Data Selector Component
 * Allows users to select and load industry data files
 */

import { useState } from 'react';
import { useIndustryData } from '@/hooks/use-industry-data';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

interface IndustryDataSelectorProps {
  onSelectData: (data: any) => void;
  onClearData: () => void;
}

export function IndustryDataSelector({ onSelectData, onClearData }: IndustryDataSelectorProps) {
  const { 
    industryData, 
    loadIndustryDataFiles, 
    selectIndustryDataFile, 
    clearSelectedFile,
    error, 
    isLoading 
  } = useIndustryData({ autoLoad: true });
  
  const [selectedFilename, setSelectedFilename] = useState<string | undefined>(undefined);

  // Handle selection change
  const handleSelectionChange = async (value: string) => {
    try {
      setSelectedFilename(value);
      const fileContent = await selectIndustryDataFile(value);
      onSelectData(fileContent);
    } catch (err) {
      console.error('Error selecting industry data file:', err);
    }
  };

  // Handle clear selection
  const handleClearSelection = () => {
    setSelectedFilename(undefined);
    clearSelectedFile();
    onClearData();
  };

  // Handle refresh data
  const handleRefreshData = () => {
    loadIndustryDataFiles();
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Industry Data</CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-2">
            <Select
              value={selectedFilename}
              onValueChange={handleSelectionChange}
              disabled={isLoading || industryData.availableFiles.length === 0}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select industry data..." />
              </SelectTrigger>
              <SelectContent>
                {industryData.availableFiles.map((file) => (
                  <SelectItem key={file.filename} value={file.filename}>
                    {file.displayName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button 
              variant="outline" 
              size="icon" 
              onClick={handleRefreshData}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-refresh-cw">
                  <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                  <path d="M21 3v5h-5" />
                  <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                  <path d="M3 21v-5h5" />
                </svg>
              )}
            </Button>
          </div>
          
          {selectedFilename && (
            <Button 
              variant="outline" 
              onClick={handleClearSelection}
              disabled={isLoading}
            >
              Clear Selection
            </Button>
          )}
          
          {industryData.availableFiles.length === 0 && !isLoading && !error && (
            <p className="text-sm text-muted-foreground">
              No industry data files available. Please check the backend configuration.
            </p>
          )}
          
          {isLoading && (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">Loading...</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
